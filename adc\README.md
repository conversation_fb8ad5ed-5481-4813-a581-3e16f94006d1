/**
 ***************************************************************************************************
 * 实验简介
 * 实验名称：ADC实验
 * 实验平台：正点原子 ESP32-S3 开发板
 * 实验目的：学习ESP32内部ADC的使用

 ***************************************************************************************************
 * 硬件资源及引脚分配
 * 1 LED
     LED - IO1
 * 2 正点原子1.3/2.4寸SPILCD模块
 * 3 ADC1
     通道7 - IO8

 ***************************************************************************************************
 * 实验现象
 * 1 ADC1采集通道7（IO8）上面的电压，并在SPILCD上显示ADC转换后电压的数字量和换算后的模拟量
 * 2 LED闪烁 ,提示程序运行

 ***************************************************************************************************
 * 注意事项
 * 使用ADC前，需将ADC的正极参考电压引脚连接至3.3V电源，才能得到准确的转换值
 * 施加到ADC采集引脚的电压需在0V~3.3V之间，否则容易烧坏芯片

 ***********************************************************************************************************
 * 公司名称：广州市星翼电子科技有限公司（正点原子）
 * 电话号码：020-38271790
 * 传真号码：020-36773971
 * 公司网址：www.alientek.com
 * 购买地址：zhengdianyuanzi.tmall.com
 * 技术论坛：http://www.openedv.com/forum.php
 * 最新资料：www.openedv.com/docs/index.html
 *
 * 在线视频：www.yuanzige.com
 * B 站视频：space.bilibili.com/394620890
 * 公 众 号：mp.weixin.qq.com/s/y--mG3qQT8gop0VRuER9bw
 * 抖    音：douyin.com/user/MS4wLjABAAAAi5E95JUBpqsW5kgMEaagtIITIl15hAJvMO8vQMV1tT6PEsw-V5HbkNLlLMkFf1Bd
 ***********************************************************************************************************
 */