/*******************************************************************************
 * Size: 26 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 26 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_26.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_26
    #define LV_FONT_MONTSERRAT_26 1
#endif

#if LV_FONT_MONTSERRAT_26

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0xff, 0x0, 0xef, 0xe0, 0xe, 0xfd, 0x0,
    0xdf, 0xd0, 0xc, 0xfc, 0x0, 0xcf, 0xb0, 0xb,
    0xfb, 0x0, 0xbf, 0xa0, 0xa, 0xfa, 0x0, 0x9f,
    0x90, 0x9, 0xf8, 0x0, 0x8f, 0x80, 0x4, 0x84,
    0x0, 0x0, 0x0, 0x1, 0x41, 0x0, 0xef, 0xe0,
    0x2f, 0xff, 0x10, 0x9f, 0x90,

    /* U+0022 "\"" */
    0x5f, 0xd0, 0xa, 0xf8, 0x5f, 0xc0, 0xa, 0xf7,
    0x4f, 0xc0, 0x9, 0xf7, 0x4f, 0xb0, 0x9, 0xf6,
    0x4f, 0xb0, 0x8, 0xf6, 0x3f, 0xa0, 0x8, 0xf6,
    0x3f, 0xa0, 0x8, 0xf5, 0x1, 0x0, 0x0, 0x10,

    /* U+0023 "#" */
    0x0, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x7f,
    0x50, 0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x6, 0xee, 0xef,
    0xfe, 0xee, 0xee, 0xff, 0xee, 0xe8, 0x0, 0x0,
    0xe, 0xf0, 0x0, 0x2, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x4, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xb0, 0x0, 0x6, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x90, 0x0, 0x8, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0, 0xa,
    0xf3, 0x0, 0x0, 0x4e, 0xee, 0xff, 0xfe, 0xee,
    0xef, 0xfe, 0xee, 0xa0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xfb, 0x0, 0x0, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x3, 0xf9, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x48, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xae, 0xff, 0xfd, 0xa5, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0xb, 0xff, 0xc6,
    0xbf, 0x57, 0xbf, 0xe0, 0x3, 0xff, 0xa0, 0x9,
    0xf0, 0x0, 0x14, 0x0, 0x6f, 0xf2, 0x0, 0x9f,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x9, 0xf0,
    0x0, 0x0, 0x0, 0x3f, 0xfc, 0x10, 0x9f, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xac, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xd8, 0x30,
    0x0, 0x0, 0x0, 0x27, 0xcf, 0xff, 0xff, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x9e, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x9, 0xf0, 0x7, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x9f, 0x0, 0xa, 0xff, 0x0,
    0x0, 0x0, 0x9, 0xf0, 0x0, 0x8f, 0xf0, 0x4c,
    0x30, 0x0, 0x9f, 0x0, 0x1e, 0xfd, 0xc, 0xff,
    0xb7, 0x4b, 0xf4, 0x7e, 0xff, 0x50, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x3, 0x9d,
    0xef, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x1a, 0xef, 0xc5, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x1e, 0xfa, 0x9d, 0xf6, 0x0,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x9, 0xf5, 0x0,
    0xd, 0xf0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0xed, 0x0, 0x0, 0x6f, 0x40, 0x0, 0x4f, 0xb0,
    0x0, 0x0, 0xf, 0xb0, 0x0, 0x4, 0xf6, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x5f, 0x50, 0xa, 0xf6, 0x0, 0x0, 0x0, 0xb,
    0xf1, 0x0, 0xa, 0xf1, 0x5, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd4, 0x38, 0xfa, 0x1, 0xee,
    0x10, 0x1, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfa,
    0x0, 0xbf, 0x40, 0x7e, 0xff, 0xb1, 0x0, 0x0,
    0x4, 0x42, 0x0, 0x6f, 0x90, 0x8f, 0xb6, 0x8f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x1f,
    0xc0, 0x0, 0x5f, 0x70, 0x0, 0x0, 0x0, 0xc,
    0xf3, 0x5, 0xf5, 0x0, 0x0, 0xec, 0x0, 0x0,
    0x0, 0x7, 0xf8, 0x0, 0x7f, 0x30, 0x0, 0xc,
    0xe0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0xce, 0x0, 0x0, 0x0, 0xcf, 0x30,
    0x0, 0x5f, 0x50, 0x0, 0xe, 0xc0, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0xfc, 0x0, 0x5, 0xf6,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x6, 0xfb,
    0x68, 0xfc, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x5, 0xcf, 0xe9, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5c, 0xff, 0xd9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xef, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xf6, 0x0, 0x1c, 0xfa, 0x0,
    0x0, 0x0, 0x8, 0xfc, 0x0, 0x0, 0x5f, 0xc0,
    0x0, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x7, 0xfb,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x40, 0x4, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xb, 0xff, 0x49, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x9c,
    0xff, 0x40, 0x0, 0x44, 0x0, 0xb, 0xfe, 0x30,
    0xc, 0xff, 0x40, 0xb, 0xf7, 0x6, 0xff, 0x20,
    0x0, 0xb, 0xff, 0x51, 0xff, 0x20, 0xbf, 0xb0,
    0x0, 0x0, 0xa, 0xff, 0xbf, 0xc0, 0xc, 0xfa,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf4, 0x0, 0xaf,
    0xf2, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x2,
    0xff, 0xe8, 0x31, 0x25, 0xaf, 0xfd, 0xff, 0x80,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8, 0xff,
    0x30, 0x1, 0x7c, 0xef, 0xec, 0x82, 0x0, 0x8,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0027 "'" */
    0x5f, 0xd5, 0xfc, 0x4f, 0xc4, 0xfb, 0x4f, 0xb3,
    0xfa, 0x3f, 0xa0, 0x10,

    /* U+0028 "(" */
    0x0, 0xb, 0xfb, 0x0, 0x3f, 0xf3, 0x0, 0xbf,
    0xb0, 0x1, 0xff, 0x50, 0x6, 0xff, 0x0, 0xb,
    0xfb, 0x0, 0xf, 0xf7, 0x0, 0x2f, 0xf4, 0x0,
    0x4f, 0xf2, 0x0, 0x6f, 0xf0, 0x0, 0x7f, 0xf0,
    0x0, 0x8f, 0xe0, 0x0, 0x8f, 0xe0, 0x0, 0x7f,
    0xf0, 0x0, 0x6f, 0xf0, 0x0, 0x4f, 0xf2, 0x0,
    0x2f, 0xf4, 0x0, 0xe, 0xf7, 0x0, 0xb, 0xfb,
    0x0, 0x6, 0xff, 0x0, 0x1, 0xff, 0x50, 0x0,
    0xbf, 0xb0, 0x0, 0x3f, 0xf3, 0x0, 0xb, 0xfb,

    /* U+0029 ")" */
    0xe, 0xf7, 0x0, 0x0, 0x6f, 0xe1, 0x0, 0x0,
    0xef, 0x80, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x3f,
    0xf3, 0x0, 0x0, 0xef, 0x80, 0x0, 0xb, 0xfb,
    0x0, 0x0, 0x8f, 0xe0, 0x0, 0x5, 0xff, 0x10,
    0x0, 0x4f, 0xf2, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x2f, 0xf4, 0x0, 0x2, 0xff, 0x40, 0x0, 0x3f,
    0xf3, 0x0, 0x4, 0xff, 0x20, 0x0, 0x5f, 0xf1,
    0x0, 0x8, 0xfe, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0xe, 0xf8, 0x0, 0x3, 0xff, 0x30, 0x0, 0x9f,
    0xd0, 0x0, 0xe, 0xf7, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0xef, 0x70, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x1, 0x0, 0x9f,
    0x0, 0x0, 0x2f, 0x91, 0x8f, 0x6, 0xe8, 0x2b,
    0xff, 0xdf, 0xcf, 0xe5, 0x0, 0x4d, 0xff, 0xf7,
    0x0, 0x0, 0x8f, 0xff, 0xfb, 0x20, 0x3e, 0xfc,
    0xbf, 0x8f, 0xf8, 0x1d, 0x50, 0x8f, 0x2, 0xb6,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x3, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x11, 0x11,
    0x9f, 0xa1, 0x11, 0x10, 0x0, 0x0, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,

    /* U+002C "," */
    0x3, 0x20, 0x6f, 0xf5, 0xbf, 0xfa, 0x6f, 0xf8,
    0xc, 0xf2, 0xf, 0xd0, 0x4f, 0x80, 0x8f, 0x20,

    /* U+002D "-" */
    0x12, 0x22, 0x22, 0x21, 0x8f, 0xff, 0xff, 0xf7,
    0x8f, 0xff, 0xff, 0xf7,

    /* U+002E "." */
    0x7, 0x70, 0x9f, 0xf8, 0xbf, 0xf9, 0x4e, 0xd2,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x18, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x3, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x0, 0x8, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x4a, 0xef, 0xfc, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0xbf, 0xfd, 0x85, 0x6b, 0xff, 0xe1, 0x0,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0x5f, 0xfb, 0x0,
    0xe, 0xfd, 0x0, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x3f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x80,
    0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd0,
    0x9f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0,
    0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0,
    0x8f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd0,
    0x3f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x80,
    0xe, 0xfd, 0x0, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x6, 0xff, 0xa0, 0x0, 0x0, 0x5f, 0xfb, 0x0,
    0x0, 0xbf, 0xfd, 0x75, 0x6b, 0xff, 0xe1, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x4a, 0xef, 0xfc, 0x60, 0x0, 0x0,

    /* U+0031 "1" */
    0xcf, 0xff, 0xff, 0xec, 0xff, 0xff, 0xfe, 0x34,
    0x44, 0xcf, 0xe0, 0x0, 0xb, 0xfe, 0x0, 0x0,
    0xbf, 0xe0, 0x0, 0xb, 0xfe, 0x0, 0x0, 0xbf,
    0xe0, 0x0, 0xb, 0xfe, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0xb, 0xfe, 0x0, 0x0, 0xbf, 0xe0, 0x0,
    0xb, 0xfe, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0xb,
    0xfe, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0xb, 0xfe,
    0x0, 0x0, 0xbf, 0xe0, 0x0, 0xb, 0xfe,

    /* U+0032 "2" */
    0x0, 0x5, 0xad, 0xff, 0xea, 0x40, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x5f,
    0xff, 0xb7, 0x56, 0x9f, 0xff, 0x90, 0x0, 0xbd,
    0x20, 0x0, 0x0, 0x1e, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x64, 0x44, 0x44, 0x44, 0x40,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,

    /* U+0033 "3" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0x44,
    0x44, 0x44, 0x4b, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x6, 0x78, 0xbf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf9, 0x2e, 0x60, 0x0, 0x0, 0x0, 0xaf,
    0xf4, 0xaf, 0xfe, 0xa6, 0x56, 0x8e, 0xff, 0xb0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x28, 0xce, 0xff, 0xda, 0x40, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xe1, 0x0, 0xa, 0xc7, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x30, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x7f, 0xf6, 0x0, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x4, 0xff, 0x90, 0x0, 0x0, 0xd,
    0xfa, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x4, 0x44, 0x44,
    0x44, 0x44, 0x4e, 0xfb, 0x44, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa,
    0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xe4, 0x44, 0x44, 0x44, 0x40, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x94,
    0x43, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xfa, 0x30, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x8f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xd0, 0xd, 0xa2, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x5, 0xff, 0xfb, 0x76, 0x67, 0xdf, 0xfe, 0x10,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x1, 0x6a, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x6, 0xbe, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x7f, 0xff, 0xa6, 0x44, 0x6b, 0xa0, 0x0, 0x3f,
    0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x1, 0x10, 0x0, 0x0, 0xa, 0xfe, 0x3, 0xaf,
    0xff, 0xfb, 0x50, 0x0, 0xbf, 0xd6, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0xb, 0xff, 0xff, 0x72, 0x1,
    0x5d, 0xff, 0x80, 0xaf, 0xff, 0x30, 0x0, 0x0,
    0xc, 0xff, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x5f, 0xf3, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x41, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5f,
    0xf3, 0x9, 0xff, 0x40, 0x0, 0x0, 0x1d, 0xfd,
    0x0, 0xd, 0xff, 0x94, 0x23, 0x6e, 0xff, 0x50,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x5, 0xae, 0xff, 0xd8, 0x20, 0x0,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x83,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x3f,
    0xf7, 0x44, 0x44, 0x44, 0x4b, 0xff, 0x33, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x3f, 0xf4,
    0x0, 0x0, 0x0, 0x6f, 0xf4, 0x1, 0x77, 0x20,
    0x0, 0x0, 0xd, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x4, 0x9d, 0xff, 0xec, 0x82, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xa,
    0xff, 0xc6, 0x32, 0x37, 0xef, 0xf5, 0x0, 0xff,
    0xb0, 0x0, 0x0, 0x1, 0xef, 0xc0, 0x2f, 0xf6,
    0x0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xdf, 0xb0, 0x8, 0xff, 0xa3,
    0x10, 0x15, 0xcf, 0xf3, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xfe, 0x70, 0x0, 0xcf, 0xfa, 0x31, 0x2,
    0x5c, 0xff, 0x80, 0x6f, 0xf6, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x2b, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf7, 0xdf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x8b, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0x6f, 0xf8, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x20, 0xdf, 0xfc, 0x53, 0x23, 0x7e, 0xff, 0x90,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x49, 0xde, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0039 "9" */
    0x0, 0x1, 0x8c, 0xef, 0xea, 0x50, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x4,
    0xff, 0xe7, 0x32, 0x49, 0xff, 0xd0, 0x0, 0xcf,
    0xd1, 0x0, 0x0, 0x3, 0xff, 0x90, 0x1f, 0xf6,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x13, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x7f, 0xf6, 0x2f, 0xf6, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x90, 0xef, 0xd1, 0x0,
    0x0, 0x3, 0xff, 0xfb, 0x6, 0xff, 0xe7, 0x32,
    0x39, 0xff, 0xff, 0xc0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x6d, 0xfc, 0x0, 0x3, 0x9d, 0xff, 0xd9,
    0x20, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf4,
    0x0, 0x9, 0xb6, 0x54, 0x5a, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x5, 0xad, 0xff, 0xeb, 0x71, 0x0, 0x0,

    /* U+003A ":" */
    0x3e, 0xd3, 0xbf, 0xfa, 0x9f, 0xf7, 0x7, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x70, 0x9f, 0xf8,
    0xbf, 0xf9, 0x4e, 0xd2,

    /* U+003B ";" */
    0x3e, 0xd3, 0xbf, 0xfa, 0x9f, 0xf7, 0x7, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x20, 0x6f, 0xf5,
    0xbf, 0xfa, 0x6f, 0xf8, 0xc, 0xf2, 0xf, 0xd0,
    0x4f, 0x80, 0x8f, 0x20,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x50, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xf6, 0x0, 0x0, 0x2,
    0x9e, 0xff, 0xf9, 0x20, 0x0, 0x6c, 0xff, 0xfc,
    0x60, 0x0, 0x19, 0xff, 0xfe, 0x82, 0x0, 0x0,
    0x4, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xfb, 0x40, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x17, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x33,

    /* U+003D "=" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60,

    /* U+003E ">" */
    0x39, 0x30, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xfd,
    0x61, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xf6, 0x0,
    0x0, 0x0, 0x3, 0x9e, 0xff, 0x60, 0x0, 0x1,
    0x6c, 0xff, 0xfb, 0x50, 0x0, 0x4a, 0xff, 0xfe,
    0x81, 0x0, 0x2, 0xdf, 0xff, 0xb4, 0x0, 0x0,
    0x0, 0x4f, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x2,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x6, 0xbd, 0xff, 0xea, 0x50, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x5f, 0xff,
    0x95, 0x34, 0x7e, 0xff, 0x90, 0x2c, 0xc1, 0x0,
    0x0, 0x1, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0x60, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x2, 0x7b, 0xdf, 0xff, 0xdb,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b,
    0xff, 0xfe, 0xcb, 0xce, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xfc, 0x51, 0x0, 0x0,
    0x1, 0x5b, 0xff, 0x70, 0x0, 0x0, 0x0, 0x9f,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf,
    0xa0, 0x0, 0x0, 0x7f, 0xd1, 0x0, 0x17, 0xcf,
    0xfc, 0x70, 0x4f, 0xf1, 0xcf, 0x70, 0x0, 0x2f,
    0xf2, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xd6, 0xff,
    0x1, 0xef, 0x20, 0xa, 0xf6, 0x0, 0x1e, 0xfe,
    0x61, 0x3, 0x9f, 0xff, 0xf0, 0x4, 0xfa, 0x1,
    0xfe, 0x0, 0xa, 0xfe, 0x20, 0x0, 0x0, 0x7f,
    0xff, 0x0, 0xc, 0xf1, 0x6f, 0x80, 0x0, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x7f,
    0x59, 0xf5, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x0, 0x4, 0xf8, 0xbf, 0x30, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x2f, 0x9b, 0xf2, 0x0, 0x5f, 0xe0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0x2, 0xf9, 0xbf, 0x30,
    0x4, 0xff, 0x10, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x3f, 0x89, 0xf5, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x0, 0x5, 0xf6, 0x6f,
    0x90, 0x0, 0x9f, 0xe2, 0x0, 0x0, 0x6, 0xff,
    0xf0, 0x0, 0xaf, 0x21, 0xfe, 0x0, 0x1, 0xef,
    0xe6, 0x10, 0x29, 0xfd, 0xff, 0x70, 0x6f, 0xc0,
    0xa, 0xf6, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xfd,
    0x2b, 0xff, 0xff, 0xf2, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x7c, 0xff, 0xc7, 0x0, 0x1a, 0xef, 0xb2,
    0x0, 0x0, 0x7f, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x61, 0x0,
    0x0, 0x2, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2b, 0xff, 0xfe, 0xcc, 0xce, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7b,
    0xdf, 0xff, 0xda, 0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xef, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x9,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x10, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xa0, 0x0, 0xaf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x3,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x40, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0xcf, 0x90, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xef, 0x81, 0x11, 0x11, 0x11, 0x11,
    0x8f, 0xf0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x70, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xd0,

    /* U+0042 "B" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x60, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x0, 0x4f, 0xf6, 0x22, 0x22, 0x23, 0x5c, 0xff,
    0xd0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x30, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf6, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x50, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf1, 0x4, 0xff, 0x62, 0x22, 0x22,
    0x35, 0xcf, 0xf7, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x14, 0xbf, 0xf8, 0x4, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x44, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x24,
    0xff, 0x62, 0x22, 0x22, 0x23, 0x6c, 0xff, 0xb0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0x50,
    0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0xa5, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x1c, 0xff, 0xfc, 0x76, 0x57, 0xcf,
    0xff, 0x60, 0xc, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x3d, 0xe2, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x1, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0xcf, 0xfc, 0x20, 0x0, 0x0, 0x3, 0xde, 0x30,
    0x1, 0xcf, 0xff, 0xc7, 0x65, 0x7c, 0xff, 0xf6,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xd4,
    0x0, 0x0, 0x0, 0x17, 0xbe, 0xff, 0xda, 0x50,
    0x0,

    /* U+0044 "D" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xc9, 0x40, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x4f, 0xf8, 0x44, 0x44, 0x46,
    0x9e, 0xff, 0xf7, 0x0, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x4, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf1, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x34, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf3, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x14, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xe0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf9, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0x10, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x5e, 0xff, 0x60, 0x4, 0xff,
    0x84, 0x44, 0x44, 0x68, 0xef, 0xff, 0x70, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x94,
    0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x4f, 0xf8,
    0x44, 0x44, 0x44, 0x44, 0x42, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf7, 0x33, 0x33, 0x33, 0x33, 0x10,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf8, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0046 "F" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x4f, 0xf8,
    0x44, 0x44, 0x44, 0x44, 0x42, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x10, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0xb6, 0x10,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x70, 0x0, 0x1c, 0xff, 0xfc, 0x86, 0x57, 0xbf,
    0xff, 0x90, 0xc, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x1a, 0xf4, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x1, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x1b, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfd, 0x9f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd6, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x1f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x8f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x0,
    0xcf, 0xfc, 0x20, 0x0, 0x0, 0x1, 0xcf, 0xd0,
    0x1, 0xcf, 0xff, 0xc8, 0x65, 0x7a, 0xff, 0xfc,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x17, 0xbe, 0xff, 0xeb, 0x71,
    0x0,

    /* U+0048 "H" */
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x64, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf6, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x64, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf6, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x64, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x64, 0xff, 0x84, 0x44, 0x44,
    0x44, 0x44, 0x6f, 0xf6, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x64, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x64, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x64, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x64,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x64, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf6,

    /* U+0049 "I" */
    0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5,
    0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5,
    0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5,
    0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5, 0x4f, 0xf5,
    0x4f, 0xf5, 0x4f, 0xf5,

    /* U+004A "J" */
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x14, 0x44, 0x44,
    0x4d, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x10,
    0x0, 0x0, 0xf, 0xfa, 0x7, 0xe2, 0x0, 0x0,
    0x6f, 0xf6, 0xe, 0xff, 0x95, 0x59, 0xff, 0xf1,
    0x3, 0xef, 0xff, 0xff, 0xff, 0x40, 0x0, 0x17,
    0xcf, 0xfd, 0x92, 0x0,

    /* U+004B "K" */
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa,
    0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x4, 0xff, 0x50, 0x3f, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x3f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x8e, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfe,
    0x3c, 0xff, 0x60, 0x0, 0x0, 0x4, 0xff, 0xfe,
    0x30, 0x1e, 0xff, 0x40, 0x0, 0x0, 0x4f, 0xff,
    0x30, 0x0, 0x2f, 0xfe, 0x20, 0x0, 0x4, 0xff,
    0x60, 0x0, 0x0, 0x4f, 0xfd, 0x10, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf6,
    0x4, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf4,

    /* U+004C "L" */
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf8, 0x44, 0x44, 0x44, 0x44, 0x40,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,

    /* U+004D "M" */
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x14, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf1, 0x4f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x14,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf1, 0x4f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x14, 0xff, 0xaf, 0xf2,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xaf, 0xf1, 0x4f,
    0xf3, 0xdf, 0xc0, 0x0, 0x0, 0x0, 0xdf, 0x96,
    0xff, 0x14, 0xff, 0x34, 0xff, 0x50, 0x0, 0x0,
    0x8f, 0xe1, 0x6f, 0xf1, 0x4f, 0xf3, 0xa, 0xfe,
    0x0, 0x0, 0x2f, 0xf6, 0x6, 0xff, 0x14, 0xff,
    0x30, 0x1f, 0xf8, 0x0, 0xa, 0xfc, 0x0, 0x6f,
    0xf1, 0x4f, 0xf3, 0x0, 0x6f, 0xf2, 0x4, 0xff,
    0x30, 0x6, 0xff, 0x14, 0xff, 0x30, 0x0, 0xdf,
    0xc0, 0xdf, 0x90, 0x0, 0x6f, 0xf1, 0x4f, 0xf3,
    0x0, 0x3, 0xff, 0xbf, 0xe1, 0x0, 0x6, 0xff,
    0x14, 0xff, 0x30, 0x0, 0x9, 0xff, 0xf6, 0x0,
    0x0, 0x6f, 0xf1, 0x4f, 0xf3, 0x0, 0x0, 0x1e,
    0xfc, 0x0, 0x0, 0x6, 0xff, 0x14, 0xff, 0x30,
    0x0, 0x0, 0x6d, 0x30, 0x0, 0x0, 0x6f, 0xf1,
    0x4f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x14, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf1,

    /* U+004E "N" */
    0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x64, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf6, 0x4f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x64, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x3f, 0xf6, 0x4f, 0xfe, 0xff, 0x80, 0x0, 0x0,
    0x3, 0xff, 0x64, 0xff, 0x6d, 0xff, 0x50, 0x0,
    0x0, 0x3f, 0xf6, 0x4f, 0xf5, 0x2f, 0xff, 0x20,
    0x0, 0x3, 0xff, 0x64, 0xff, 0x50, 0x4f, 0xfe,
    0x10, 0x0, 0x3f, 0xf6, 0x4f, 0xf5, 0x0, 0x7f,
    0xfc, 0x0, 0x3, 0xff, 0x64, 0xff, 0x50, 0x0,
    0xaf, 0xf9, 0x0, 0x3f, 0xf6, 0x4f, 0xf5, 0x0,
    0x0, 0xdf, 0xf6, 0x3, 0xff, 0x64, 0xff, 0x50,
    0x0, 0x1, 0xef, 0xf3, 0x3f, 0xf6, 0x4f, 0xf5,
    0x0, 0x0, 0x3, 0xff, 0xe5, 0xff, 0x64, 0xff,
    0x50, 0x0, 0x0, 0x6, 0xff, 0xef, 0xf6, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x64,
    0xff, 0x50, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf6,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0x64, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf6,

    /* U+004F "O" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfe, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xfc,
    0x76, 0x68, 0xcf, 0xff, 0xa0, 0x0, 0x0, 0xbf,
    0xfc, 0x20, 0x0, 0x0, 0x3, 0xdf, 0xf9, 0x0,
    0x8, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x60, 0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xe0, 0x6f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x9f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0xbf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x9f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x6f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x1f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xe0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x60, 0x0, 0xcf, 0xfc, 0x20,
    0x0, 0x0, 0x3, 0xdf, 0xfa, 0x0, 0x0, 0x1c,
    0xff, 0xfb, 0x76, 0x68, 0xcf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x7b, 0xef, 0xfe,
    0xb6, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x4f, 0xff, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x4f, 0xf8, 0x44, 0x44, 0x57, 0xbf, 0xff, 0x30,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0x4f, 0xf5, 0x0, 0x0, 0x2, 0x7e, 0xff, 0x60,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0,
    0x4f, 0xf8, 0x44, 0x44, 0x32, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfe, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xfc, 0x76, 0x68, 0xcf, 0xff, 0xa0, 0x0, 0x0,
    0xb, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x3d, 0xff,
    0x90, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x50, 0x1, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x6f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf3, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x70, 0xbf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf8, 0xb, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x80, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x30, 0x1f, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xd0,
    0x0, 0x9f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf6, 0x0, 0x0, 0xdf, 0xfb, 0x10, 0x0,
    0x0, 0x2, 0xcf, 0xfb, 0x0, 0x0, 0x2, 0xdf,
    0xff, 0xa6, 0x45, 0x6b, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xdf, 0xff,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xe2, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf7, 0x22,
    0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xfe, 0xb5, 0x0,

    /* U+0052 "R" */
    0x4f, 0xff, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x4f, 0xf8, 0x44, 0x44, 0x57, 0xbf, 0xff, 0x30,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x5, 0xff, 0xd0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf6,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0x4f, 0xf5, 0x0, 0x0, 0x2, 0x7e, 0xff, 0x60,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x4f, 0xf7, 0x33, 0x33, 0x3c, 0xfe, 0x10, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x10,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x2, 0xff, 0xb0,
    0x4f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf6,

    /* U+0053 "S" */
    0x0, 0x3, 0x9d, 0xef, 0xec, 0x94, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xa,
    0xff, 0xc7, 0x43, 0x47, 0xbf, 0xe0, 0x3, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x25, 0x0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0x94,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7c,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf0, 0x5d, 0x40, 0x0, 0x0, 0x0, 0x1e, 0xfc,
    0xc, 0xff, 0xd9, 0x54, 0x35, 0x8e, 0xff, 0x40,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x2, 0x7b, 0xef, 0xfe, 0xb7, 0x10, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x44, 0x44, 0x44, 0xcf, 0xf4, 0x44, 0x44, 0x40,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfb,
    0xe, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf6,
    0x7, 0xff, 0xb0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0xcf, 0xfe, 0x96, 0x57, 0xbf, 0xff, 0x50,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x39, 0xdf, 0xfe, 0xc7, 0x10, 0x0,

    /* U+0056 "V" */
    0xc, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf4, 0x5, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xd0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xef, 0xa0, 0x0, 0x0, 0x0,
    0xbf, 0xf1, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0xd, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0,
    0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x60, 0x0, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xd0, 0x3, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf5, 0xa, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfc, 0x1f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xbf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0xf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x3, 0xff, 0x40, 0xaf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xe0, 0x5, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0xf, 0xfb, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0x20, 0x0, 0x0, 0x3,
    0xff, 0x40, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0,
    0xff, 0x6f, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0x0, 0x4, 0xff, 0x50, 0x0, 0x0, 0x6f, 0xf0,
    0xbf, 0xc0, 0x0, 0x0, 0xe, 0xf9, 0x0, 0x0,
    0xf, 0xfb, 0x0, 0x0, 0xb, 0xfa, 0x5, 0xff,
    0x20, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0xaf,
    0xf0, 0x0, 0x1, 0xff, 0x50, 0xf, 0xf7, 0x0,
    0x0, 0x9f, 0xe0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x6f, 0xf0, 0x0, 0xaf, 0xd0, 0x0, 0xe,
    0xf9, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x0, 0xc,
    0xfa, 0x0, 0x5, 0xff, 0x20, 0x4, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x2, 0xff, 0x40,
    0x0, 0xf, 0xf8, 0x0, 0x9f, 0xe0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x50, 0x7f, 0xe0, 0x0, 0x0,
    0x9f, 0xd0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfb, 0xd, 0xf9, 0x0, 0x0, 0x4, 0xff,
    0x34, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf4, 0xff, 0x40, 0x0, 0x0, 0xe, 0xf8, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xdf,
    0xe0, 0x0, 0x0, 0x0, 0x9f, 0xef, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xd, 0xff, 0x20, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x50, 0x2, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x3f,
    0xf9, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0x1,
    0xef, 0xc0, 0x0, 0x0, 0xa, 0xff, 0x40, 0x0,
    0xb, 0xff, 0x20, 0x0, 0x0, 0x1, 0xef, 0xe1,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfc, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x8d, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xdf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0x19,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf4,
    0x0, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x6, 0xff, 0x90, 0x0, 0x0,
    0xcf, 0xf2, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x0,
    0x8, 0xff, 0x60, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x10, 0x4f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xc0,

    /* U+0059 "Y" */
    0xc, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf9, 0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xe1, 0x0, 0x9f, 0xf3, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x60, 0x0, 0xe, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xfc, 0x0, 0x0, 0x5, 0xff, 0x60,
    0x0, 0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0xbf,
    0xf1, 0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x2f, 0xfa, 0x0, 0xa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x40, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xd1, 0xdf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfd, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb0, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4c, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfe, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+005B "[" */
    0x4f, 0xff, 0xff, 0x24, 0xff, 0xff, 0xf2, 0x4f,
    0xf4, 0x11, 0x4, 0xff, 0x30, 0x0, 0x4f, 0xf3,
    0x0, 0x4, 0xff, 0x30, 0x0, 0x4f, 0xf3, 0x0,
    0x4, 0xff, 0x30, 0x0, 0x4f, 0xf3, 0x0, 0x4,
    0xff, 0x30, 0x0, 0x4f, 0xf3, 0x0, 0x4, 0xff,
    0x30, 0x0, 0x4f, 0xf3, 0x0, 0x4, 0xff, 0x30,
    0x0, 0x4f, 0xf3, 0x0, 0x4, 0xff, 0x30, 0x0,
    0x4f, 0xf3, 0x0, 0x4, 0xff, 0x30, 0x0, 0x4f,
    0xf3, 0x0, 0x4, 0xff, 0x30, 0x0, 0x4f, 0xf3,
    0x0, 0x4, 0xff, 0x41, 0x10, 0x4f, 0xff, 0xff,
    0x24, 0xff, 0xff, 0xf2,

    /* U+005C "\\" */
    0x78, 0x20, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x60, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xa0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0,

    /* U+005D "]" */
    0x8f, 0xff, 0xfe, 0x8f, 0xff, 0xfe, 0x1, 0x19,
    0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0,
    0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe,
    0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9,
    0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0,
    0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe,
    0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9,
    0xfe, 0x0, 0x9, 0xfe, 0x0, 0x9, 0xfe, 0x1,
    0x19, 0xfe, 0x8f, 0xff, 0xfe, 0x8f, 0xff, 0xfe,

    /* U+005E "^" */
    0x0, 0x0, 0xa, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xaf, 0xa0, 0x0, 0x0, 0x0, 0xe, 0xf0, 0xdf,
    0x10, 0x0, 0x0, 0x5, 0xf9, 0x6, 0xf7, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x1f, 0xe0, 0x0, 0x0,
    0x2f, 0xc0, 0x0, 0x9f, 0x50, 0x0, 0x9, 0xf6,
    0x0, 0x3, 0xfb, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0xc, 0xf2, 0x0, 0x6f, 0x90, 0x0, 0x0, 0x6f,
    0x90, 0xd, 0xf2, 0x0, 0x0, 0x0, 0xff, 0x0,

    /* U+005F "_" */
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x58, 0x83, 0x0, 0x0, 0xaf, 0xf4, 0x0, 0x0,
    0x7f, 0xf4, 0x0, 0x0, 0x4e, 0xf4,

    /* U+0061 "a" */
    0x0, 0x39, 0xdf, 0xfe, 0xb6, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0xe, 0xfa, 0x63,
    0x36, 0xdf, 0xf8, 0x0, 0x42, 0x0, 0x0, 0x0,
    0xcf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf4, 0x0,
    0x6b, 0xef, 0xff, 0xff, 0xff, 0x40, 0xbf, 0xff,
    0xdd, 0xdd, 0xdf, 0xf4, 0x6f, 0xf7, 0x0, 0x0,
    0x3, 0xff, 0x4a, 0xfd, 0x0, 0x0, 0x0, 0x3f,
    0xf4, 0xaf, 0xe0, 0x0, 0x0, 0xa, 0xff, 0x45,
    0xff, 0x80, 0x0, 0x1a, 0xff, 0xf4, 0xa, 0xff,
    0xfd, 0xdf, 0xfa, 0xff, 0x40, 0x6, 0xcf, 0xfe,
    0xb5, 0x1f, 0xf4,

    /* U+0062 "b" */
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x2,
    0x9e, 0xfe, 0xc6, 0x0, 0x0, 0xaf, 0xd6, 0xff,
    0xff, 0xff, 0xfd, 0x30, 0xa, 0xff, 0xff, 0xa5,
    0x35, 0xaf, 0xff, 0x20, 0xaf, 0xff, 0x40, 0x0,
    0x0, 0x5f, 0xfc, 0xa, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x8f, 0xf3, 0xaf, 0xf1, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x7a, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x9a, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1f,
    0xf7, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x3a, 0xff, 0xf4, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0xaf, 0xff, 0xfa, 0x53, 0x5a, 0xff, 0xf2, 0xa,
    0xfc, 0x6f, 0xff, 0xff, 0xff, 0xd3, 0x0, 0xaf,
    0xc0, 0x39, 0xef, 0xec, 0x60, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x1, 0x7c, 0xef, 0xea, 0x50, 0x0, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xb0, 0x4, 0xff, 0xf9,
    0x43, 0x5b, 0xff, 0xa1, 0xef, 0xe2, 0x0, 0x0,
    0x7, 0xd3, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x20, 0x0, 0x0, 0x7d, 0x40,
    0x4f, 0xff, 0x94, 0x35, 0xbf, 0xfa, 0x0, 0x4e,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x17, 0xce,
    0xfe, 0xa4, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x18,
    0xde, 0xfd, 0x81, 0x2f, 0xf5, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xe5, 0xff, 0x50, 0x5f, 0xff, 0x84,
    0x36, 0xcf, 0xff, 0xf5, 0x1f, 0xfe, 0x20, 0x0,
    0x0, 0x8f, 0xff, 0x57, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0xf5, 0xbf, 0xd0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x5d, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf5, 0xdf, 0xa0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x5b, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf5, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x51, 0xff, 0xd1, 0x0, 0x0, 0x6, 0xff, 0xf5,
    0x5, 0xff, 0xe6, 0x21, 0x3a, 0xff, 0xff, 0x50,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xf5, 0x0,
    0x2, 0x8d, 0xff, 0xd8, 0x10, 0xff, 0x50,

    /* U+0065 "e" */
    0x0, 0x2, 0x8d, 0xff, 0xd8, 0x10, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x5, 0xff,
    0xd5, 0x22, 0x5d, 0xff, 0x40, 0x1f, 0xfb, 0x0,
    0x0, 0x0, 0xbf, 0xe0, 0x7f, 0xf1, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0xbf, 0xb0, 0x0, 0x0, 0x0,
    0xa, 0xfa, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xdf, 0xed, 0xdd, 0xdd, 0xdd, 0xdd, 0xda,
    0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfd,
    0x20, 0x0, 0x0, 0x19, 0x0, 0x4, 0xff, 0xf9,
    0x43, 0x48, 0xef, 0xa0, 0x0, 0x4e, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x1, 0x6c, 0xef, 0xeb,
    0x60, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x5c, 0xff, 0xd7, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xc0, 0x0, 0xf, 0xfc, 0x31, 0x43,
    0x0, 0x3, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f,
    0xf2, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x9f, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x14,
    0xff, 0x41, 0x11, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x4f, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0x4, 0xff, 0x30, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x2, 0x8d, 0xef, 0xd9, 0x20, 0xdf, 0x80,
    0x7, 0xff, 0xff, 0xff, 0xff, 0x6d, 0xf8, 0x7,
    0xff, 0xe8, 0x43, 0x49, 0xff, 0xff, 0x82, 0xff,
    0xc1, 0x0, 0x0, 0x3, 0xff, 0xf8, 0x8f, 0xf2,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x8c, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf8, 0xef, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x8c, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf8, 0x9f, 0xf1, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x83, 0xff, 0xb0, 0x0, 0x0,
    0x2, 0xef, 0xf8, 0x9, 0xff, 0xd5, 0x10, 0x27,
    0xef, 0xff, 0x80, 0xa, 0xff, 0xff, 0xff, 0xff,
    0x9f, 0xf8, 0x0, 0x5, 0xbf, 0xff, 0xfc, 0x40,
    0xff, 0x80, 0x0, 0x0, 0x2, 0x20, 0x0, 0x1f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x30, 0x4a, 0x10, 0x0, 0x0, 0x1, 0xef, 0xe0,
    0xe, 0xff, 0xa6, 0x43, 0x48, 0xef, 0xf5, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x5, 0xad, 0xff, 0xec, 0x82, 0x0, 0x0,

    /* U+0068 "h" */
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xd0, 0x3a, 0xef, 0xfc,
    0x60, 0x0, 0xaf, 0xd8, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xaf, 0xff, 0xf8, 0x54, 0x6d, 0xff, 0x90,
    0xaf, 0xfe, 0x20, 0x0, 0x0, 0xdf, 0xf1, 0xaf,
    0xf5, 0x0, 0x0, 0x0, 0x5f, 0xf4, 0xaf, 0xf0,
    0x0, 0x0, 0x0, 0x2f, 0xf6, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xaf, 0xd0, 0x0, 0x0, 0x0,
    0xf, 0xf7, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0xf,
    0xf7, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0xf, 0xf7,

    /* U+0069 "i" */
    0x8, 0xfb, 0x0, 0xff, 0xf3, 0xc, 0xfe, 0x10,
    0x3, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0xa,
    0xfd, 0x0, 0xaf, 0xd0, 0xa, 0xfd, 0x0, 0xaf,
    0xd0, 0xa, 0xfd, 0x0, 0xaf, 0xd0, 0xa, 0xfd,
    0x0, 0xaf, 0xd0, 0xa, 0xfd, 0x0, 0xaf, 0xd0,
    0xa, 0xfd, 0x0, 0xaf, 0xd0, 0xa, 0xfd, 0x0,

    /* U+006A "j" */
    0x0, 0x0, 0x6, 0xfc, 0x10, 0x0, 0x0, 0xef,
    0xf6, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0, 0x0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x8, 0xff, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x8,
    0xff, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0,
    0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0x8f, 0xf0,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x0, 0x8f,
    0xf0, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x0,
    0x8f, 0xf0, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x74,
    0x26, 0xff, 0x90, 0x2f, 0xff, 0xff, 0xe1, 0x1,
    0xad, 0xfe, 0xa2, 0x0,

    /* U+006B "k" */
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x3,
    0xef, 0xd1, 0xaf, 0xd0, 0x0, 0x0, 0x4f, 0xfc,
    0x10, 0xaf, 0xd0, 0x0, 0x5, 0xff, 0xc1, 0x0,
    0xaf, 0xd0, 0x0, 0x6f, 0xfc, 0x10, 0x0, 0xaf,
    0xd0, 0x7, 0xff, 0xc1, 0x0, 0x0, 0xaf, 0xd0,
    0x8f, 0xfd, 0x10, 0x0, 0x0, 0xaf, 0xd9, 0xff,
    0xff, 0x20, 0x0, 0x0, 0xaf, 0xff, 0xfc, 0xff,
    0xd1, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x7f, 0xfb,
    0x0, 0x0, 0xaf, 0xf9, 0x0, 0xa, 0xff, 0x70,
    0x0, 0xaf, 0xd0, 0x0, 0x0, 0xdf, 0xf4, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0x2f, 0xfe, 0x10, 0xaf,
    0xd0, 0x0, 0x0, 0x4, 0xff, 0xc0, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x8f, 0xf9,

    /* U+006C "l" */
    0xaf, 0xda, 0xfd, 0xaf, 0xda, 0xfd, 0xaf, 0xda,
    0xfd, 0xaf, 0xda, 0xfd, 0xaf, 0xda, 0xfd, 0xaf,
    0xda, 0xfd, 0xaf, 0xda, 0xfd, 0xaf, 0xda, 0xfd,
    0xaf, 0xda, 0xfd, 0xaf, 0xd0,

    /* U+006D "m" */
    0xaf, 0xc0, 0x5b, 0xef, 0xea, 0x40, 0x0, 0x5a,
    0xef, 0xeb, 0x50, 0x0, 0xaf, 0xca, 0xff, 0xff,
    0xff, 0xf8, 0xb, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xaf, 0xff, 0xd6, 0x22, 0x6e, 0xff, 0xdf, 0xe7,
    0x32, 0x5d, 0xff, 0x60, 0xaf, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xfe, 0x20, 0x0, 0x0, 0xef, 0xd0,
    0xaf, 0xf4, 0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0xf1, 0xaf, 0xf0, 0x0, 0x0,
    0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf4,

    /* U+006E "n" */
    0xaf, 0xc0, 0x4a, 0xef, 0xfc, 0x60, 0x0, 0xaf,
    0xc9, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xaf, 0xff,
    0xe6, 0x32, 0x4c, 0xff, 0x90, 0xaf, 0xfd, 0x10,
    0x0, 0x0, 0xcf, 0xf1, 0xaf, 0xf4, 0x0, 0x0,
    0x0, 0x4f, 0xf4, 0xaf, 0xf0, 0x0, 0x0, 0x0,
    0x1f, 0xf6, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0xf, 0xf7, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0xf, 0xf7, 0xaf, 0xd0, 0x0,
    0x0, 0x0, 0xf, 0xf7, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0xf, 0xf7, 0xaf, 0xd0, 0x0, 0x0, 0x0,
    0xf, 0xf7,

    /* U+006F "o" */
    0x0, 0x1, 0x7c, 0xef, 0xda, 0x40, 0x0, 0x0,
    0x5, 0xef, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x4,
    0xff, 0xf8, 0x43, 0x5c, 0xff, 0xc0, 0x1, 0xff,
    0xe2, 0x0, 0x0, 0x8, 0xff, 0x80, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0xb, 0xfe, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf3, 0xdf, 0xa0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x5d, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0xbf, 0xd0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x37, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xbf, 0xe0, 0x1e, 0xfe, 0x20, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x4f, 0xff, 0x84, 0x36, 0xcf,
    0xfc, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0x17, 0xce, 0xfd, 0xa4, 0x0,
    0x0,

    /* U+0070 "p" */
    0xaf, 0xc0, 0x3a, 0xef, 0xec, 0x60, 0x0, 0xa,
    0xfc, 0x8f, 0xff, 0xff, 0xff, 0xd3, 0x0, 0xaf,
    0xff, 0xf8, 0x31, 0x38, 0xff, 0xf2, 0xa, 0xff,
    0xf3, 0x0, 0x0, 0x3, 0xff, 0xc0, 0xaf, 0xf6,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x3a, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf7, 0xaf, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x9a, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf9, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x7a, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x8f, 0xf3, 0xaf, 0xff, 0x40, 0x0, 0x0,
    0x5f, 0xfc, 0xa, 0xff, 0xff, 0xa5, 0x35, 0xaf,
    0xff, 0x20, 0xaf, 0xd6, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0xa, 0xfd, 0x2, 0x9d, 0xfe, 0xc6, 0x0,
    0x0, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x1, 0x8d, 0xef, 0xd8, 0x10, 0xff, 0x50,
    0x6, 0xff, 0xff, 0xff, 0xfe, 0x4f, 0xf5, 0x5,
    0xff, 0xf8, 0x43, 0x6c, 0xff, 0xff, 0x51, 0xff,
    0xe2, 0x0, 0x0, 0x8, 0xff, 0xf5, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x5b, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf5, 0xdf, 0xa0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x5d, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0xbf, 0xd0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x57, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xbf, 0xf5, 0x1f, 0xfe, 0x20, 0x0, 0x0,
    0x8f, 0xff, 0x50, 0x5f, 0xff, 0x84, 0x36, 0xcf,
    0xff, 0xf5, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xe5,
    0xff, 0x50, 0x0, 0x28, 0xdf, 0xfd, 0x81, 0x2f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50,

    /* U+0072 "r" */
    0xaf, 0xc0, 0x3a, 0xec, 0xaf, 0xc7, 0xff, 0xfc,
    0xaf, 0xef, 0xfb, 0x75, 0xaf, 0xff, 0x30, 0x0,
    0xaf, 0xf6, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0xaf, 0xe0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x5, 0xbe, 0xff, 0xda, 0x50, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x8, 0xff, 0xa4,
    0x23, 0x59, 0xf2, 0x0, 0xdf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xfd, 0xa6, 0x0, 0x0, 0x0, 0x6b,
    0xef, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x14,
    0x9f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf3, 0x4, 0x20, 0x0, 0x0, 0x5, 0xff, 0x30,
    0xef, 0xb6, 0x33, 0x47, 0xff, 0xd0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x5, 0xad, 0xff,
    0xec, 0x71, 0x0,

    /* U+0074 "t" */
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x20, 0x9f, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x14, 0xff, 0x41, 0x11,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x4f, 0xf3, 0x0, 0x0, 0x0, 0x4, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x60, 0x0, 0x0, 0x0, 0xe, 0xfe,
    0x52, 0x55, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x5c, 0xff, 0xc6, 0x0,

    /* U+0075 "u" */
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0xcf,
    0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0xcf, 0xb0,
    0x0, 0x0, 0x0, 0x4f, 0xf3, 0xcf, 0xb0, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0xcf, 0xb0, 0x0, 0x0,
    0x0, 0x4f, 0xf3, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x4f, 0xf3, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x4f,
    0xf3, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0xbf, 0xd0, 0x0, 0x0, 0x0, 0x6f, 0xf3, 0x9f,
    0xf0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x5f, 0xf7,
    0x0, 0x0, 0x5, 0xff, 0xf3, 0xe, 0xff, 0x83,
    0x13, 0x8f, 0xff, 0xf3, 0x3, 0xef, 0xff, 0xff,
    0xff, 0x7f, 0xf3, 0x0, 0x18, 0xdf, 0xfd, 0x91,
    0x2f, 0xf3,

    /* U+0076 "v" */
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x6, 0xff, 0x20, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0xef, 0x90, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x1f, 0xf7, 0x0, 0x0, 0xd, 0xf9, 0x0,
    0x0, 0xa, 0xfd, 0x0, 0x0, 0x4f, 0xf3, 0x0,
    0x0, 0x3, 0xff, 0x40, 0x0, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0xdf, 0xb0, 0x2, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x6f, 0xf2, 0x8, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf8, 0xe, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xfe, 0x7f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0xaf, 0xb0, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0xe, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x9, 0xfc, 0x0, 0x0,
    0x9, 0xfd, 0xff, 0x10, 0x0, 0x4, 0xfe, 0x0,
    0x3, 0xff, 0x20, 0x0, 0xf, 0xf4, 0xdf, 0x60,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x5f, 0xe0, 0x8f, 0xc0, 0x0, 0x1f, 0xf3, 0x0,
    0x0, 0x7f, 0xd0, 0x0, 0xbf, 0x80, 0x2f, 0xf2,
    0x0, 0x6f, 0xd0, 0x0, 0x0, 0x1f, 0xf3, 0x1,
    0xff, 0x20, 0xc, 0xf8, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0xb, 0xf9, 0x7, 0xfc, 0x0, 0x6, 0xfe,
    0x2, 0xff, 0x10, 0x0, 0x0, 0x5, 0xfe, 0xd,
    0xf6, 0x0, 0x0, 0xff, 0x48, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x8f, 0xf0, 0x0, 0x0, 0xaf,
    0x9d, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x90, 0x0, 0x0, 0x4f, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x30, 0x0, 0x0, 0xe,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfd,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x30, 0x0, 0x0,

    /* U+0078 "x" */
    0x1e, 0xfc, 0x0, 0x0, 0x0, 0x6f, 0xf5, 0x4,
    0xff, 0x80, 0x0, 0x2, 0xff, 0x80, 0x0, 0x7f,
    0xf4, 0x0, 0xd, 0xfc, 0x0, 0x0, 0xb, 0xfe,
    0x10, 0xaf, 0xe1, 0x0, 0x0, 0x1, 0xef, 0xc6,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfe, 0xfc, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x83, 0xff, 0x80, 0x0, 0x0, 0x1e,
    0xfc, 0x0, 0x7f, 0xf4, 0x0, 0x0, 0xbf, 0xe1,
    0x0, 0xb, 0xfe, 0x20, 0x8, 0xff, 0x40, 0x0,
    0x1, 0xef, 0xc0, 0x4f, 0xf8, 0x0, 0x0, 0x0,
    0x3f, 0xf9,

    /* U+0079 "y" */
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5,
    0x6, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x0, 0x8f, 0xf1, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xd, 0xf9, 0x0,
    0x0, 0x9, 0xfe, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0x0, 0x2, 0xff, 0x60, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xd0, 0x2, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x8, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfb, 0xf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x8f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0xd, 0x73, 0x3a, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xcf, 0xeb, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x1, 0x11, 0x11, 0x14,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x1e, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x50, 0x0, 0x0, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xaf,
    0xf3, 0x0, 0x0, 0x0, 0x7, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x4f, 0xfa, 0x11, 0x11, 0x11, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9,

    /* U+007B "{" */
    0x0, 0x1, 0x9d, 0xfa, 0x0, 0xc, 0xff, 0xfa,
    0x0, 0x3f, 0xfa, 0x20, 0x0, 0x6f, 0xf2, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x1, 0xbf, 0xf0, 0x0, 0x8f, 0xff, 0x60, 0x0,
    0x8f, 0xff, 0x70, 0x0, 0x0, 0xaf, 0xf0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x7f, 0xf1, 0x0, 0x0, 0x7f, 0xf1, 0x0,
    0x0, 0x6f, 0xf2, 0x0, 0x0, 0x3f, 0xfa, 0x20,
    0x0, 0xc, 0xff, 0xfa, 0x0, 0x1, 0x9d, 0xfa,

    /* U+007C "|" */
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,
    0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0, 0x4f, 0xf0,

    /* U+007D "}" */
    0x8f, 0xea, 0x10, 0x0, 0x8f, 0xff, 0xe0, 0x0,
    0x1, 0x9f, 0xf6, 0x0, 0x0, 0xf, 0xf8, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xc, 0xfd, 0x20, 0x0, 0x4, 0xef, 0xfa,
    0x0, 0x5, 0xff, 0xfa, 0x0, 0xd, 0xfc, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xe, 0xf9, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x0, 0xf, 0xf8, 0x0, 0x1, 0x9f, 0xf6, 0x0,
    0x8f, 0xff, 0xe0, 0x0, 0x8f, 0xea, 0x10, 0x0,

    /* U+007E "~" */
    0x1, 0xaf, 0xfa, 0x20, 0x0, 0x1f, 0x80, 0xcf,
    0xff, 0xff, 0x60, 0x8, 0xf6, 0x3f, 0xb1, 0x17,
    0xff, 0xed, 0xfe, 0x6, 0xf3, 0x0, 0x2, 0xae,
    0xfb, 0x20,

    /* U+00B0 "°" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x19, 0xff, 0xe8,
    0x0, 0xd, 0xe7, 0x58, 0xfb, 0x7, 0xf2, 0x0,
    0x4, 0xf5, 0xcb, 0x0, 0x0, 0xe, 0xac, 0xb0,
    0x0, 0x0, 0xda, 0x8f, 0x10, 0x0, 0x3f, 0x71,
    0xed, 0x52, 0x5e, 0xd0, 0x2, 0xdf, 0xff, 0xc2,
    0x0, 0x0, 0x24, 0x20, 0x0,

    /* U+2022 "•" */
    0x2, 0xab, 0x40, 0xe, 0xff, 0xf2, 0x3f, 0xff,
    0xf5, 0x1f, 0xff, 0xf3, 0x5, 0xee, 0x70,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x9c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x26, 0xbf, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xdf, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x5a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x3,
    0x7c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x6f,
    0xff, 0x20, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc7, 0x30, 0x1, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xfe, 0xa5, 0x10,
    0x0, 0x0, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x5,
    0xff, 0xfd, 0x83, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x20,
    0x0, 0x0, 0x5, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x5f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x20, 0x0, 0x0, 0x5, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x20, 0x0, 0x0,
    0x5, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x58,
    0x98, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x5f, 0xfe,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x5, 0xff, 0xe0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x2,
    0x44, 0x7f, 0xfe, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x5d, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5f, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf4, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x4,
    0xac, 0xdb, 0x81, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x9f, 0xff, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x34, 0x0, 0x18, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x81, 0x0, 0x43, 0xea, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0xae, 0xff, 0xee, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xcc, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xfd, 0xcc, 0xff, 0xfa, 0x0, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0xaf, 0xfa, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x0, 0xaf, 0xfb, 0x0,
    0x3f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf3, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xfe, 0xaa, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfc, 0xaa, 0xef, 0xfa, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0xaf, 0xfa, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xaf, 0xfc,
    0x22, 0x5f, 0xfb, 0x77, 0x77, 0x77, 0x77, 0x77,
    0xbf, 0xf5, 0x22, 0xcf, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xfe, 0x88, 0xaf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xfa, 0x88, 0xef, 0xfa, 0x0,
    0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf2, 0x0, 0xaf, 0xfa, 0x0, 0x2f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0, 0xaf,
    0xfc, 0x44, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf6, 0x44, 0xcf, 0xff, 0xff, 0xff,
    0xfb, 0x77, 0x77, 0x77, 0x77, 0x77, 0xbf, 0xff,
    0xff, 0xff, 0xfd, 0x66, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x66, 0xdf, 0xa9,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x9a,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xf4, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xf7, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x37, 0x77, 0x77, 0x60, 0x1,
    0x67, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xf4, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xf7, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x37, 0x88, 0x88, 0x70, 0x2,
    0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xe3, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xf8, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x48, 0x88, 0x88, 0x71, 0x2,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x84,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x53, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0xbd, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0xc, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xfe, 0x20, 0x9, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xe3, 0x9f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x20, 0x3f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xe2, 0xef, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xfb, 0xdf, 0xff, 0xff, 0xa0, 0x0,
    0xb, 0xff, 0xff, 0xfb, 0x3f, 0xff, 0xff, 0xfa,
    0x0, 0xbf, 0xff, 0xff, 0xe2, 0x3, 0xff, 0xff,
    0xff, 0xab, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xfa, 0x0, 0xb, 0xff, 0xff,
    0xfe, 0x23, 0xff, 0xff, 0xff, 0xa0, 0xaf, 0xff,
    0xff, 0xe2, 0x0, 0x3f, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0x8f, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xf6, 0x9, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xef, 0x70, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xbb, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x2f,
    0xff, 0xe0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xf5, 0x0, 0x2f, 0xff, 0xe0, 0x0,
    0x8f, 0xc1, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfe,
    0x0, 0x2f, 0xff, 0xe0, 0x2, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0x30, 0x2f, 0xff,
    0xe0, 0x6, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xbf,
    0xff, 0xfa, 0x0, 0x2f, 0xff, 0xe0, 0x0, 0xcf,
    0xff, 0xf8, 0x0, 0x5, 0xff, 0xff, 0x90, 0x0,
    0x2f, 0xff, 0xe0, 0x0, 0xc, 0xff, 0xff, 0x20,
    0xc, 0xff, 0xfc, 0x0, 0x0, 0x2f, 0xff, 0xe0,
    0x0, 0x1, 0xff, 0xff, 0x90, 0x2f, 0xff, 0xf3,
    0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0, 0x0, 0x7f,
    0xff, 0xe0, 0x6f, 0xff, 0xc0, 0x0, 0x0, 0x2f,
    0xff, 0xe0, 0x0, 0x0, 0x1f, 0xff, 0xf3, 0x9f,
    0xff, 0x80, 0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0,
    0x0, 0xb, 0xff, 0xf6, 0xbf, 0xff, 0x60, 0x0,
    0x0, 0x2f, 0xff, 0xe0, 0x0, 0x0, 0x9, 0xff,
    0xf7, 0xbf, 0xff, 0x50, 0x0, 0x0, 0x2f, 0xff,
    0xe0, 0x0, 0x0, 0x8, 0xff, 0xf8, 0xbf, 0xff,
    0x60, 0x0, 0x0, 0xa, 0xdd, 0x70, 0x0, 0x0,
    0xa, 0xff, 0xf6, 0x8f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf5,
    0x5f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf1, 0xf, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xc0, 0x9, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x60, 0x2,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xfd, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x18, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xfe, 0x85, 0x34,
    0x59, 0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0x9c, 0xdc, 0xb9, 0x50, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x55, 0x31,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x5d, 0xff, 0xff, 0xff, 0xd5, 0x0, 0x6, 0x10,
    0x0, 0x0, 0xd, 0xfd, 0x5c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc5, 0xdf, 0xd0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x33, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xb, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf6, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x4f, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xf4,
    0x0, 0x0, 0x7, 0xe5, 0x4, 0xdf, 0xff, 0xff,
    0xff, 0xfd, 0x40, 0x5d, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x9b, 0xdd, 0xc9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xfd, 0x30,
    0x0, 0x3f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf6, 0x0,
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0x80, 0x4f,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xbf, 0xff, 0xfb, 0x5f, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xf5, 0x3, 0xef, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xfe,
    0x30, 0x20, 0x1c, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xc1, 0x9,
    0xfb, 0x10, 0xaf, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xfa, 0x1, 0xbf, 0xff,
    0xd2, 0x7, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x70, 0x2d, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xfc, 0x10, 0x0, 0x1, 0xcf,
    0xff, 0xf4, 0x4, 0xef, 0xff, 0xff, 0xff, 0xf7,
    0x2, 0xdf, 0xff, 0xe3, 0x0, 0x3e, 0xff, 0xfd,
    0x20, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x1b, 0xff, 0xff, 0x50, 0xef, 0xff, 0xc1, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x9f, 0xff, 0xf2, 0x6f, 0xf9, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x6,
    0xff, 0xa0, 0x7, 0x60, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x39,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0x83, 0x33, 0x5f,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0x30, 0x0, 0xf, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0x30, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0x30, 0x0, 0xf, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0x20, 0x0, 0xe, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbb, 0xbb, 0xb9, 0x0,
    0x0, 0x7, 0xbb, 0xbb, 0xba, 0x10, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xad, 0xdd, 0xda,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x11, 0x14, 0xff, 0xff, 0xff, 0x41, 0x11, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xe3, 0x9,
    0xff, 0x90, 0x3e, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x77, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xfd, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x6, 0xf0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x4a, 0xf5, 0x7f, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x3, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x30,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x3, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe,
    0x10, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xa0,
    0x0, 0x2, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf5, 0x0,
    0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe, 0x10, 0x6f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xa0, 0xef, 0xff,
    0xcc, 0xcc, 0xcc, 0x40, 0x0, 0x0, 0x0, 0x1b,
    0xcc, 0xcc, 0xce, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x88, 0x88, 0x8b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x40,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x77, 0x52,
    0x0, 0x0, 0x0, 0x6e, 0xec, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xe8, 0x10, 0x0,
    0x9f, 0xff, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x8f, 0xff, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2, 0x7f, 0xff, 0x0, 0x3, 0xef, 0xff,
    0xfe, 0x84, 0x22, 0x59, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0x0, 0x1e, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x18, 0xff, 0xff, 0xff, 0xff, 0x0, 0xbf,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0x3, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xb, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xf, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x17,
    0x87, 0x10, 0x0, 0x0, 0x0, 0x0, 0x17, 0x88,
    0x88, 0x88, 0x88, 0x86, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xd0, 0xff, 0xff, 0xfe, 0x56, 0x77, 0x81,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x60, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xf4,
    0x0, 0xff, 0xfd, 0xff, 0xff, 0xfa, 0x30, 0x0,
    0x3, 0x9f, 0xff, 0xff, 0x80, 0x0, 0xff, 0xf7,
    0x8f, 0xff, 0xff, 0xff, 0xdd, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0xf8, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0xff, 0xf8, 0x0, 0x7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0xff, 0xf9, 0x0,
    0x0, 0x3, 0x8b, 0xdd, 0xb9, 0x51, 0x0, 0x0,
    0x0, 0x0, 0x35, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xf6, 0xbb, 0xbb, 0xbd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x6b, 0xbb, 0xbb, 0xdf, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3, 0xb5, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x9, 0xff, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2,
    0xdf, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x2f, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xf7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x6f, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x6,
    0xff, 0xd0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xfe, 0x20, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x40, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf0, 0x0, 0x0, 0x6d, 0x40, 0x2, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf0, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x5f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x3e, 0xff, 0x50, 0xb, 0xff, 0x0,
    0x6b, 0xbb, 0xbb, 0xdf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x1, 0xdf, 0xf2, 0x3, 0xff, 0x60, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x3, 0xc6,
    0x0, 0x3f, 0xf9, 0x0, 0xdf, 0xb0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x9, 0xff, 0x70,
    0xa, 0xff, 0x0, 0x8f, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1, 0xdf, 0xf1, 0x5,
    0xff, 0x30, 0x5f, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x1f, 0xf6, 0x2, 0xff,
    0x50, 0x3f, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xf, 0xf7, 0x1, 0xff, 0x50,
    0x3f, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x6f, 0xf4, 0x3, 0xff, 0x40, 0x4f,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x6, 0xff, 0xd0, 0x7, 0xff, 0x10, 0x6f, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8,
    0xfd, 0x20, 0xd, 0xfc, 0x0, 0xbf, 0xd0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x40,
    0x0, 0x8f, 0xf5, 0x1, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x7,
    0xff, 0xb0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xf0, 0x0, 0x0, 0x9f, 0xfe,
    0x10, 0x1e, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf0, 0x0, 0x0, 0xcf, 0xc1, 0x0,
    0xbf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xf0, 0x0, 0x0, 0x15, 0x0, 0x9, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x50, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x7, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x70, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x72, 0x4d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfa, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x80, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x40, 0x2b, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x7f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x7, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xf7, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x2, 0xcb, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x10, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xef, 0xfb, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xf4, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xef, 0xf6, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xbf, 0xfb, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x6f, 0xff, 0x40, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xe, 0xff, 0xf3,
    0x0, 0x3a, 0xff, 0xff, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0x93, 0x7, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xff, 0xfd, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33, 0x10,
    0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd2, 0xbf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x7b, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf8, 0xbf, 0xfe, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x8b, 0xff, 0xe0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xf8, 0xbf, 0xfe, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x8b, 0xff, 0xe0, 0x1, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0xbf, 0xfe, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x8b, 0xff, 0xe2, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xbf, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xbf,
    0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b,
    0xff, 0xe0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xbf, 0xfe, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0x8b, 0xff, 0xe0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xf8, 0xbf, 0xfe, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x8b, 0xff, 0xe0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xf8, 0xbf, 0xfe, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x8b, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf8, 0xbf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x66, 0xbb, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8b, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe5, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfe, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xa0, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x7a, 0xbb, 0xbb, 0xa5, 0x0, 0x0, 0x7,
    0xab, 0xbb, 0xba, 0x50,

    /* U+F04D "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x7a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xba, 0x50,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xee, 0x30, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf8, 0xaf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x9a, 0xff, 0xff, 0x50, 0x0, 0x0, 0x1,
    0xff, 0xf9, 0xaf, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x1f, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xf9, 0xaf, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x1f, 0xff, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x1, 0xff, 0xf9, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x1f, 0xff, 0x9a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc2, 0xff, 0xf9, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0x9a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x9a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x71, 0xff, 0xf9,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0x60, 0x1f, 0xff,
    0x9a, 0xff, 0xff, 0xff, 0xff, 0x50, 0x1, 0xff,
    0xf9, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x1f,
    0xff, 0x9a, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x1,
    0xff, 0xf9, 0xaf, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x1f, 0xff, 0x9a, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x1, 0xff, 0xf9, 0x8f, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x91, 0x99, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xab, 0xb5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x6, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x28, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x87, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0,

    /* U+F054 "" */
    0x2, 0xc8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0xa, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xfe, 0x20, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x23, 0x33, 0x33, 0x33, 0xff, 0xff, 0xd3, 0x33,
    0x33, 0x33, 0x10, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xce, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x3a, 0xbb, 0xbb, 0xbb, 0xbf, 0xff, 0xff,
    0xbb, 0xbb, 0xbb, 0xb9, 0x10, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xa8, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa4, 0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xb2,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x56, 0x77,
    0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xfa, 0x40, 0x0, 0x38, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xe2,
    0x0, 0x3, 0x98, 0x30, 0x0, 0xcf, 0xff, 0xff,
    0x30, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x50, 0x0,
    0x4, 0xff, 0xfa, 0x0, 0x2f, 0xff, 0xff, 0xe2,
    0x0, 0x9, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xa0, 0x9, 0xff, 0xff, 0xfd, 0x0,
    0x3f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf2, 0x4, 0xff, 0xff, 0xff, 0x70, 0xcf,
    0xff, 0xff, 0xf6, 0x1, 0xc9, 0xdf, 0xff, 0xff,
    0xf7, 0x2, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff,
    0xff, 0xf5, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1, 0xff, 0xff, 0xff, 0xf2, 0x8f, 0xff, 0xff,
    0xf6, 0x1, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2,
    0xff, 0xff, 0xff, 0xc0, 0xd, 0xff, 0xff, 0xfa,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xe0, 0x6, 0xff,
    0xff, 0xff, 0x30, 0x3, 0xff, 0xff, 0xff, 0x10,
    0x1d, 0xff, 0xff, 0xff, 0x40, 0xd, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xa0, 0x1,
    0x9f, 0xff, 0xb3, 0x0, 0x7f, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x10, 0x0, 0x5, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x1, 0x8f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xff, 0xc8, 0x78, 0xbf,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7a, 0xdf, 0xff, 0xdb, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x9, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x25, 0x77, 0x65, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0x5a,
    0xef, 0xff, 0xff, 0xff, 0xe9, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfc, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xf8, 0x30, 0x1, 0x4b, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xb1, 0x7, 0x97, 0x10, 0x4, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x5, 0xff, 0xff, 0xd3, 0xaf, 0xff, 0x50, 0x7,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x1e, 0xa0,
    0x0, 0x2, 0xdf, 0xff, 0xfd, 0xff, 0xff, 0x40,
    0xe, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x9, 0xff,
    0xd2, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xaf, 0xff, 0xff, 0xf2, 0x0, 0x2, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x4f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0x10, 0x7f, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xf0, 0x9, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xb1, 0xdf, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xef, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf8,
    0x10, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff,
    0xff, 0xa8, 0x78, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0x0, 0xa, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x8b, 0xef, 0xfe, 0xc8, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x80, 0x0, 0x4f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xf6, 0x0, 0x3, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf8, 0x0, 0x4, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xee,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x3d,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x72, 0x5e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x13, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x10, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfd, 0x10, 0xdf, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xd1, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x1, 0x11, 0x1b, 0xff, 0xfc, 0x1, 0xdf, 0xff,
    0xf8, 0x18, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xcf, 0xd1, 0x1d, 0xff, 0xff, 0x80, 0x8, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0xa, 0x10, 0xcf,
    0xff, 0xf9, 0x0, 0x6, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xc0,
    0x26, 0x0, 0x4, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfc, 0x1, 0xdf, 0x50, 0x7,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xd1, 0x1d, 0xff, 0xf5, 0x8, 0xff, 0xfc, 0x10,
    0xde, 0xee, 0xef, 0xff, 0xfd, 0x10, 0x3f, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x12, 0x22, 0x22, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x29, 0xff, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x52, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x91, 0xdf, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x90, 0x1, 0xdf,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x90, 0x0, 0x1, 0xdf, 0xff, 0xfb, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfb, 0x0, 0x3f, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xfb, 0xb, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xf5, 0x5f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10, 0x6e, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcc,
    0x10,

    /* U+F078 "" */
    0x1, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x40, 0x1, 0xdf, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x70, 0xaf,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0x48, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf2, 0xa, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xf4, 0x0, 0xa, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xe2, 0x0, 0x7, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xe2, 0x7, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xe9, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x3, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x2d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x4, 0x44, 0x44, 0x44, 0x44, 0x4e, 0xff,
    0x60, 0x0, 0x0, 0xcf, 0xff, 0x6f, 0xff, 0x6f,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf6, 0x0, 0x0, 0x6, 0xff, 0x42, 0xff, 0xf2,
    0x4f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x3, 0x20, 0x2f, 0xff,
    0x20, 0x23, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbe, 0x40, 0xdf, 0xf6, 0xa, 0xf5, 0x0, 0x0,
    0x2, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x3d, 0xff, 0x69, 0xff, 0xf1, 0x0,
    0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfe, 0xef, 0xfd, 0xff, 0xfd, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x5, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x5, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x40, 0x0, 0x5, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x4, 0x78, 0x88, 0x88, 0x88, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8b, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xee, 0xee, 0xff, 0xff, 0xff, 0xfe, 0xee, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x20, 0xff,
    0xff, 0xff, 0x62, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x7b, 0xbb, 0xb9, 0x15,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xba, 0xaa,
    0xab, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xfd, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x6, 0xf0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x4a, 0xf5, 0x7f, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x3, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x30,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xb8, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xfd, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x6c, 0xb0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x28,
    0xef, 0xff, 0xa0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff,
    0x70, 0x1a, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xeb, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xd5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x20, 0x0,
    0xb, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xfd, 0x20, 0x6f, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xa0,
    0xcf, 0xfd, 0x35, 0xff, 0xf8, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xfd, 0x10, 0xff, 0xf5, 0x0, 0x9f,
    0xfb, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xd1, 0x0,
    0xff, 0xf6, 0x0, 0xaf, 0xfb, 0x0, 0xc, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0xbf, 0xff, 0x89, 0xff,
    0xf8, 0x0, 0xcf, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0x4c, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xce, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0xdf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xfc, 0x18, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0xcf, 0xfd, 0x35, 0xff,
    0xf8, 0x0, 0x8f, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0xff, 0xf5, 0x0, 0x9f, 0xfb, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x40, 0x0, 0xff, 0xf6, 0x0, 0xaf,
    0xfb, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf3, 0x0,
    0xbf, 0xff, 0x89, 0xff, 0xf7, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0x30, 0x4f, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x90,
    0x6, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x7, 0xef, 0xe8, 0x0, 0x0, 0x3a, 0xcc, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x4c, 0xdd, 0xdd, 0xdd, 0xdd,
    0x11, 0xb2, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1f, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x11,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x1f, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x11, 0xff,
    0xff, 0xb9, 0xff, 0xfd, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xf, 0xff, 0xfe, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xe0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xfe, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xe0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xfe, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xfe,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xe0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xfe, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xe0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xfe, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xe0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xfe, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xbf, 0xff, 0xff, 0x41, 0x89, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x82, 0xff, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x13, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff, 0xf6,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x24, 0xff, 0xff,
    0x80, 0xf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0x70, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0x4f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf8, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0x9f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xf9, 0xff, 0xf9, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x45, 0xff, 0xff, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x10, 0x4d,
    0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x87, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x41, 0x9d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdc, 0x60,

    /* U+F0C9 "" */
    0x8b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x69, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x69, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0E0 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x10, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0xb2, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x4e,
    0xff, 0x60, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd2, 0x8, 0xff, 0xff, 0xfa, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x2,
    0xcf, 0xff, 0xff, 0xff, 0xe4, 0x5, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x1c, 0xff, 0xff, 0xff, 0xc1,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x8f, 0xff, 0xf8, 0x3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2, 0x99,
    0x20, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x3b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2,

    /* U+F0E7 "" */
    0x0, 0x9, 0xaa, 0xaa, 0xaa, 0xa9, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xa8, 0x88, 0x88,
    0x60, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x6c, 0xc8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x33, 0x33, 0x8f, 0xff,
    0xfb, 0x33, 0x33, 0x10, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xf7, 0x5f, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x0, 0xbf,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xf5, 0x3f, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xd0,
    0x7d, 0xdd, 0xdd, 0xdd, 0x21, 0xb2, 0x0, 0xf,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xf3,
    0x1f, 0xe2, 0x0, 0xff, 0xff, 0xff, 0x80, 0xef,
    0xff, 0xff, 0xff, 0x31, 0xff, 0xe2, 0xf, 0xff,
    0xff, 0xf8, 0xe, 0xff, 0xff, 0xff, 0xf3, 0x1f,
    0xff, 0xe2, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff,
    0xff, 0xff, 0x31, 0xff, 0xff, 0xbf, 0xff, 0xff,
    0xf8, 0xe, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xff, 0xa2, 0x22, 0x22, 0x1f, 0xff, 0xff, 0xf8,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xf8, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0x80, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xf8, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xef, 0xff,
    0xff, 0x80, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x56, 0x66, 0x63, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff,
    0xff, 0xfc, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x12, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x42, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xf4, 0x0, 0x9f,
    0x20, 0xb, 0xf1, 0x0, 0xcf, 0x0, 0xf, 0xc0,
    0x1, 0xff, 0xf4, 0xff, 0xf4, 0x0, 0x8f, 0x10,
    0xa, 0xf0, 0x0, 0xbe, 0x0, 0xe, 0xb0, 0x0,
    0xff, 0xf4, 0xff, 0xf5, 0x0, 0xaf, 0x20, 0xb,
    0xf1, 0x0, 0xcf, 0x0, 0xf, 0xc0, 0x1, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xaa, 0xbf, 0xea, 0xac, 0xfe,
    0xaa, 0xdf, 0xda, 0xad, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xfb, 0x0, 0x1f, 0x80, 0x2, 0xf7, 0x0,
    0x4f, 0x40, 0x7, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xfb, 0x0, 0x1f, 0x80, 0x2, 0xf7, 0x0, 0x4f,
    0x40, 0x7, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xfc,
    0x0, 0x3f, 0xa0, 0x4, 0xf9, 0x0, 0x6f, 0x60,
    0x8, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xfc, 0x99, 0xef, 0xb9,
    0x99, 0x99, 0x99, 0x99, 0x99, 0xaf, 0xf9, 0x9a,
    0xff, 0xf4, 0xff, 0xf4, 0x0, 0x8f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xb0, 0x0, 0xff,
    0xf4, 0xff, 0xf4, 0x0, 0x8f, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xb0, 0x0, 0xff, 0xf4,
    0xff, 0xf8, 0x44, 0xbf, 0x64, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x4f, 0xd4, 0x45, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x40,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x17, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3a, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x3, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xad, 0xee, 0xee, 0xee, 0xee,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x83, 0x7, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xf, 0xc1, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xf, 0xfc, 0x10, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xf, 0xff, 0xc1, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xf, 0xff,
    0xfc, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xf, 0xff, 0xff, 0xc0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xf, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xdd,
    0xdd, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xad, 0xff,
    0xff, 0xff, 0xeb, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x17, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x40,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xfc,
    0x84, 0x20, 0x0, 0x1, 0x36, 0xae, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x2d, 0xff, 0xff, 0xfe, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbf,
    0xff, 0xff, 0xf7, 0xd, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xff, 0xff, 0xf5, 0x8f, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfe, 0x10, 0x8f, 0xa0, 0x0,
    0x0, 0x0, 0x38, 0xcd, 0xef, 0xec, 0xa6, 0x10,
    0x0, 0x0, 0x4, 0xfe, 0x20, 0x0, 0x30, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa3, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xfb, 0x63, 0x21, 0x24,
    0x8d, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xdf, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xae, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbd, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x79, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x1b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xf, 0xff, 0x74, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xaf,
    0xfe, 0x90, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x4f, 0xff, 0x42, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xf5, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4, 0x8f, 0xff, 0x5f, 0xff, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xef, 0xf5, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xe, 0xff, 0x5f, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x13, 0xff, 0xf5, 0xff, 0xf4, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xff, 0xff, 0x5f, 0xff, 0x41,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x80, 0x8f, 0xff, 0xf5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0,

    /* U+F241 "" */
    0x1b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xf, 0xff, 0x74, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xaf,
    0xfe, 0x90, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x4f, 0xff, 0x42, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x4, 0x8f, 0xff, 0x5f, 0xff, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x5f, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf5, 0xff, 0xf4, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x5f, 0xff, 0x41,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x10, 0x0, 0x0, 0x8f, 0xff, 0xf5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0,

    /* U+F242 "" */
    0x1b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xf, 0xff, 0x74, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xaf,
    0xfe, 0x90, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x4f, 0xff, 0x42, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x8f, 0xff, 0x5f, 0xff, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x5f, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0xff, 0xf5, 0xff, 0xf4, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x5f, 0xff, 0x41,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x82, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xf5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0,

    /* U+F243 "" */
    0x1b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xf, 0xff, 0x74, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xaf,
    0xfe, 0x90, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x4f, 0xff, 0x41, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xff, 0xf4, 0x1f, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x8f, 0xff, 0x5f, 0xff, 0x41, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0xff, 0xf4, 0x1f, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x5f, 0xff, 0x41, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0xff, 0xf5, 0xff, 0xf4, 0x1f,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x5f, 0xff, 0x40,
    0x88, 0x88, 0x88, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xf5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0,

    /* U+F244 "" */
    0x1b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0xf, 0xff, 0x74, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0xaf,
    0xfe, 0x90, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x4f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x8f, 0xff, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf5, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x5f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x13, 0xff, 0xf5, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x5f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xf5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0,
    0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x77, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbc, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfe, 0x31, 0x5f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x5c, 0xc5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x50, 0x0, 0x0,
    0x3f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xd2, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc8, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xc0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfe, 0x50, 0xe, 0xff, 0xff, 0xff,
    0xcc, 0xff, 0xec, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x2, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfa, 0x10, 0x2e, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xd3, 0x0, 0x0, 0x18,
    0xb9, 0x20, 0x0, 0x0, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xa0,
    0x0, 0x33, 0x33, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x30, 0x1f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfe, 0x77, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x56, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x33, 0x30, 0x0,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x26, 0xac, 0xdc, 0xb8, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x2, 0xdf,
    0xff, 0xfe, 0x9f, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xe0, 0xaf, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xfe, 0x0,
    0xbf, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0xcf, 0xff, 0xff, 0x30, 0x5,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1, 0xdf, 0xff,
    0xf8, 0x0, 0xaf, 0xff, 0xd9, 0xff, 0xe0, 0x3a,
    0x1, 0xdf, 0xff, 0xc0, 0xd, 0xff, 0xf1, 0x8,
    0xfe, 0x2, 0xfa, 0x2, 0xef, 0xff, 0x0, 0xff,
    0xff, 0xb0, 0x8, 0xe0, 0x2f, 0x50, 0x5f, 0xff,
    0xf1, 0x1f, 0xff, 0xff, 0xa0, 0x7, 0x2, 0x50,
    0x4f, 0xff, 0xff, 0x22, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xf3, 0x3f, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x1e, 0xff, 0xff, 0xff,
    0x43, 0xff, 0xff, 0xff, 0xff, 0x40, 0x7, 0xff,
    0xff, 0xff, 0xf4, 0x3f, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x9, 0xff, 0xff, 0xff, 0x32, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf3,
    0xf, 0xff, 0xff, 0x50, 0x1c, 0x2, 0xa0, 0xb,
    0xff, 0xff, 0x20, 0xef, 0xff, 0x50, 0x1d, 0xf0,
    0x2f, 0xa0, 0xd, 0xff, 0xf0, 0xc, 0xff, 0xf4,
    0x1d, 0xff, 0x2, 0xf5, 0x4, 0xff, 0xfe, 0x0,
    0x8f, 0xff, 0xfd, 0xff, 0xf0, 0x25, 0x3, 0xff,
    0xff, 0xa0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xf6, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xf0, 0x3, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xf3, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xbf, 0xff, 0xff, 0xea, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x4, 0xbc, 0xcc, 0xcc, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x22, 0x22, 0x22, 0xef,
    0xff, 0xff, 0xff, 0xa2, 0x22, 0x22, 0x10, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10, 0x1, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x40,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x6, 0xff, 0xfd, 0x1d, 0xff,
    0xb1, 0xef, 0xf9, 0x2f, 0xff, 0xf2, 0x0, 0x6f,
    0xff, 0xb0, 0xbf, 0xf9, 0xd, 0xff, 0x70, 0xff,
    0xff, 0x20, 0x6, 0xff, 0xfb, 0xb, 0xff, 0x90,
    0xdf, 0xf7, 0xf, 0xff, 0xf2, 0x0, 0x6f, 0xff,
    0xb0, 0xbf, 0xf9, 0xd, 0xff, 0x70, 0xff, 0xff,
    0x20, 0x6, 0xff, 0xfb, 0xb, 0xff, 0x90, 0xdf,
    0xf7, 0xf, 0xff, 0xf2, 0x0, 0x6f, 0xff, 0xb0,
    0xbf, 0xf9, 0xd, 0xff, 0x70, 0xff, 0xff, 0x20,
    0x6, 0xff, 0xfb, 0xb, 0xff, 0x90, 0xdf, 0xf7,
    0xf, 0xff, 0xf2, 0x0, 0x6f, 0xff, 0xb0, 0xbf,
    0xf9, 0xd, 0xff, 0x70, 0xff, 0xff, 0x20, 0x6,
    0xff, 0xfb, 0xb, 0xff, 0x90, 0xdf, 0xf7, 0xf,
    0xff, 0xf2, 0x0, 0x6f, 0xff, 0xb0, 0xbf, 0xf9,
    0xd, 0xff, 0x70, 0xff, 0xff, 0x20, 0x6, 0xff,
    0xfb, 0xb, 0xff, 0x90, 0xdf, 0xf7, 0xf, 0xff,
    0xf2, 0x0, 0x6f, 0xff, 0xb0, 0xbf, 0xf9, 0xd,
    0xff, 0x70, 0xff, 0xff, 0x20, 0x6, 0xff, 0xfd,
    0x1d, 0xff, 0xb1, 0xef, 0xf9, 0x2f, 0xff, 0xf2,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x2, 0x34, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x31, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7b, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb5, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf5, 0x7, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5,
    0x7, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xf5, 0x7, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf5, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xec, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x78, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x87, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xdf, 0xff, 0xff, 0xda, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x1, 0xdf, 0xff, 0xd1, 0x8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x1, 0xdf, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0x80, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x1, 0xb1, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xf8, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0x8c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x86, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x3,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x80, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x7,
    0xf7, 0x0, 0x1, 0xef, 0xff, 0xff, 0xf8, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x7,
    0xff, 0xf7, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x27,
    0xff, 0xff, 0xf7, 0x2e, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x20,

    /* U+F7C2 "" */
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xd9, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x1d,
    0xfc, 0x44, 0xfc, 0x44, 0xfc, 0x44, 0xff, 0xf5,
    0x1, 0xdf, 0xfb, 0x0, 0xeb, 0x0, 0xeb, 0x0,
    0xef, 0xf5, 0x1d, 0xff, 0xfb, 0x0, 0xeb, 0x0,
    0xeb, 0x0, 0xef, 0xf5, 0xdf, 0xff, 0xfb, 0x0,
    0xeb, 0x0, 0xeb, 0x0, 0xef, 0xf5, 0xff, 0xff,
    0xfb, 0x0, 0xeb, 0x0, 0xeb, 0x0, 0xef, 0xf5,
    0xff, 0xff, 0xff, 0xdd, 0xff, 0xdd, 0xff, 0xdd,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x0,
    0x1, 0xaa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0x10, 0x0, 0x1, 0xdf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf1, 0x0, 0x2, 0xdf, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x10, 0x2,
    0xef, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf1, 0x3, 0xef, 0xff, 0xff,
    0x86, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0xff,
    0xff, 0x14, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x7, 0xff, 0xff, 0xff, 0xcb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa7, 0x0,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xee, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 112, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 111, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 45, .adv_w = 163, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 77, .adv_w = 292, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 239, .adv_w = 258, .box_w = 15, .box_h = 25, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 427, .adv_w = 351, .box_w = 21, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 616, .adv_w = 285, .box_w = 17, .box_h = 19, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 778, .adv_w = 87, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 790, .adv_w = 140, .box_w = 6, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 862, .adv_w = 141, .box_w = 7, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 946, .adv_w = 166, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 996, .adv_w = 242, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1074, .adv_w = 94, .box_w = 4, .box_h = 8, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1090, .adv_w = 159, .box_w = 8, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 1102, .adv_w = 94, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1110, .adv_w = 146, .box_w = 12, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1260, .adv_w = 277, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1404, .adv_w = 154, .box_w = 7, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1467, .adv_w = 239, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1602, .adv_w = 238, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1728, .adv_w = 278, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1890, .adv_w = 239, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2025, .adv_w = 257, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2160, .adv_w = 249, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2295, .adv_w = 268, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2430, .adv_w = 257, .box_w = 15, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2565, .adv_w = 94, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2593, .adv_w = 94, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2629, .adv_w = 242, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2707, .adv_w = 242, .box_w = 13, .box_h = 9, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 2766, .adv_w = 242, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2844, .adv_w = 238, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2970, .adv_w = 430, .box_w = 25, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3258, .adv_w = 305, .box_w = 21, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3447, .adv_w = 315, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3600, .adv_w = 301, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3753, .adv_w = 344, .box_w = 19, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3924, .adv_w = 279, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4050, .adv_w = 264, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4176, .adv_w = 321, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4329, .adv_w = 338, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4482, .adv_w = 129, .box_w = 4, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4518, .adv_w = 213, .box_w = 12, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4626, .adv_w = 299, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4779, .adv_w = 247, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4905, .adv_w = 397, .box_w = 21, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5094, .adv_w = 338, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5247, .adv_w = 349, .box_w = 20, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5427, .adv_w = 300, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5571, .adv_w = 349, .box_w = 21, .box_h = 22, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5802, .adv_w = 302, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5946, .adv_w = 258, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6081, .adv_w = 244, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6225, .adv_w = 329, .box_w = 16, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6369, .adv_w = 296, .box_w = 20, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6549, .adv_w = 468, .box_w = 29, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6810, .adv_w = 280, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6972, .adv_w = 269, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7134, .adv_w = 273, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7278, .adv_w = 139, .box_w = 7, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 7362, .adv_w = 146, .box_w = 11, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 7500, .adv_w = 139, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7572, .adv_w = 243, .box_w = 13, .box_h = 11, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 7644, .adv_w = 208, .box_w = 13, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7657, .adv_w = 250, .box_w = 7, .box_h = 4, .ofs_x = 3, .ofs_y = 16},
    {.bitmap_index = 7671, .adv_w = 249, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7762, .adv_w = 284, .box_w = 15, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7905, .adv_w = 238, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7996, .adv_w = 284, .box_w = 15, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8139, .adv_w = 255, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8237, .adv_w = 147, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8342, .adv_w = 287, .box_w = 15, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8485, .adv_w = 283, .box_w = 14, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8618, .adv_w = 116, .box_w = 5, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8666, .adv_w = 118, .box_w = 9, .box_h = 24, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 8774, .adv_w = 256, .box_w = 14, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8907, .adv_w = 116, .box_w = 3, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8936, .adv_w = 440, .box_w = 24, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9104, .adv_w = 283, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9202, .adv_w = 264, .box_w = 15, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9307, .adv_w = 284, .box_w = 15, .box_h = 19, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 9450, .adv_w = 284, .box_w = 15, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9593, .adv_w = 171, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9649, .adv_w = 208, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9740, .adv_w = 172, .box_w = 11, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9834, .adv_w = 282, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9932, .adv_w = 233, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10044, .adv_w = 374, .box_w = 24, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10212, .adv_w = 230, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10310, .adv_w = 233, .box_w = 16, .box_h = 19, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 10462, .adv_w = 217, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10546, .adv_w = 146, .box_w = 8, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10642, .adv_w = 124, .box_w = 4, .box_h = 24, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 10690, .adv_w = 146, .box_w = 8, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 10786, .adv_w = 242, .box_w = 13, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 10812, .adv_w = 174, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 10857, .adv_w = 131, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 10872, .adv_w = 416, .box_w = 27, .box_h = 28, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11250, .adv_w = 416, .box_w = 26, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11510, .adv_w = 416, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11822, .adv_w = 416, .box_w = 26, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12082, .adv_w = 286, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12253, .adv_w = 416, .box_w = 26, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12591, .adv_w = 416, .box_w = 26, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12942, .adv_w = 468, .box_w = 30, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13302, .adv_w = 416, .box_w = 26, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13653, .adv_w = 468, .box_w = 30, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13953, .adv_w = 416, .box_w = 26, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14304, .adv_w = 208, .box_w = 13, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14441, .adv_w = 312, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14651, .adv_w = 468, .box_w = 30, .box_h = 26, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15041, .adv_w = 416, .box_w = 26, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15301, .adv_w = 286, .box_w = 18, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15544, .adv_w = 364, .box_w = 17, .box_h = 25, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 15757, .adv_w = 364, .box_w = 23, .box_h = 28, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 16079, .adv_w = 364, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16355, .adv_w = 364, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16631, .adv_w = 364, .box_w = 17, .box_h = 25, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 16844, .adv_w = 364, .box_w = 25, .box_h = 24, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 17144, .adv_w = 260, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 17305, .adv_w = 260, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 17466, .adv_w = 364, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17742, .adv_w = 364, .box_w = 23, .box_h = 6, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 17811, .adv_w = 468, .box_w = 30, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18111, .adv_w = 520, .box_w = 33, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18557, .adv_w = 468, .box_w = 31, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 18976, .adv_w = 416, .box_w = 26, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19288, .adv_w = 364, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 19449, .adv_w = 364, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 19610, .adv_w = 520, .box_w = 33, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 19957, .adv_w = 416, .box_w = 26, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 20217, .adv_w = 416, .box_w = 26, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 20568, .adv_w = 416, .box_w = 27, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 20933, .adv_w = 364, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21221, .adv_w = 364, .box_w = 23, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 21532, .adv_w = 364, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21808, .adv_w = 364, .box_w = 23, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 22050, .adv_w = 416, .box_w = 26, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22310, .adv_w = 260, .box_w = 18, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 22553, .adv_w = 364, .box_w = 23, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 22864, .adv_w = 364, .box_w = 23, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 23175, .adv_w = 468, .box_w = 30, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23475, .adv_w = 416, .box_w = 28, .box_h = 28, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 23867, .adv_w = 312, .box_w = 20, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 24137, .adv_w = 520, .box_w = 33, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24533, .adv_w = 520, .box_w = 33, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 24814, .adv_w = 520, .box_w = 33, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 25095, .adv_w = 520, .box_w = 33, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 25376, .adv_w = 520, .box_w = 33, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 25657, .adv_w = 520, .box_w = 33, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 25938, .adv_w = 520, .box_w = 33, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 26285, .adv_w = 364, .box_w = 21, .box_h = 27, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 26569, .adv_w = 364, .box_w = 23, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 26880, .adv_w = 416, .box_w = 27, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 27245, .adv_w = 520, .box_w = 33, .box_h = 20, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27575, .adv_w = 312, .box_w = 20, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 27845, .adv_w = 418, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 19, 0, 11, -9, 0, 0,
    0, 0, -23, -25, 3, 20, 9, 7,
    -17, 3, 20, 1, 17, 4, 13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 25, 3, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 0, -12, 0, 0, 0, 0,
    0, -8, 7, 8, 0, 0, -4, 0,
    -3, 4, 0, -4, 0, -4, -2, -8,
    0, 0, 0, 0, -4, 0, 0, -5,
    -6, 0, 0, -4, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -6, 0, -11, 0, -50, 0,
    0, -8, 0, 8, 12, 0, 0, -8,
    4, 4, 14, 8, -7, 8, 0, 0,
    -24, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -15, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -11, -5, -20, 0, -17,
    -3, 0, 0, 0, 0, 1, 16, 0,
    -12, -3, -1, 1, 0, -7, 0, 0,
    -3, -31, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -33, -3, 16,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 14,
    0, 4, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 16, 3,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -15, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    8, 4, 12, -4, 0, 0, 8, -4,
    -14, -57, 3, 11, 8, 1, -5, 0,
    15, 0, 13, 0, 13, 0, -39, 0,
    -5, 12, 0, 14, -4, 8, 4, 0,
    0, 1, -4, 0, 0, -7, 33, 0,
    33, 0, 12, 0, 17, 5, 7, 12,
    0, 0, 0, -15, 0, 0, 0, 0,
    1, -3, 0, 3, -7, -5, -8, 3,
    0, -4, 0, 0, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -23, 0, -26, 0, 0, 0,
    0, -3, 0, 41, -5, -5, 4, 4,
    -4, 0, -5, 4, 0, 0, -22, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -40, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -26, 0, 25, 0, 0, -15, 0,
    14, 0, -28, -40, -28, -8, 12, 0,
    0, -28, 0, 5, -10, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 11, 12, -51, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 20, 0, 3, 0, 0, 0,
    0, 0, 3, 3, -5, -8, 0, -1,
    -1, -4, 0, 0, -3, 0, 0, 0,
    -8, 0, -3, 0, -10, -8, 0, -10,
    -14, -14, -8, 0, -8, 0, -8, 0,
    0, 0, 0, -3, 0, 0, 4, 0,
    3, -4, 0, 1, 0, 0, 0, 4,
    -3, 0, 0, 0, -3, 4, 4, -1,
    0, 0, 0, -8, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 5, -3, 0,
    -5, 0, -7, 0, 0, -3, 0, 12,
    0, 0, -4, 0, 0, 0, 0, 0,
    -1, 1, -3, -3, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -4, -5, 0,
    0, 0, 0, 0, 1, 0, 0, -3,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -3, -5, 0, -6, 0, -12,
    -3, -12, 8, 0, 0, -8, 4, 8,
    11, 0, -10, -1, -5, 0, -1, -20,
    4, -3, 3, -22, 4, 0, 0, 1,
    -22, 0, -22, -3, -36, -3, 0, -21,
    0, 8, 12, 0, 5, 0, 0, 0,
    0, 1, 0, -7, -5, 0, -12, 0,
    0, 0, -4, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -5, 0, 0, 0, 0, 0, 0, 0,
    -4, -4, 0, -3, -5, -3, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -5,
    0, -3, 0, -8, 4, 0, 0, -5,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -5, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -5, -6, 0,
    -8, 0, 12, -3, 1, -13, 0, 0,
    11, -21, -22, -17, -8, 4, 0, -3,
    -27, -7, 0, -7, 0, -8, 6, -7,
    -27, 0, -11, 0, 0, 2, -1, 3,
    -3, 0, 4, 0, -12, -16, 0, -21,
    -10, -9, -10, -12, -5, -11, -1, -8,
    -11, 2, 0, 1, 0, -4, 0, 0,
    0, 3, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -2, 0, -1, -4, 0, -7, -9,
    -9, -1, 0, -12, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 2,
    -2, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 20, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -8, 0, 0, 0, 0, -21, -12, 0,
    0, 0, -6, -21, 0, 0, -4, 4,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -7, 0,
    0, 0, 0, 5, 0, 3, -8, -8,
    0, -4, -4, -5, 0, 0, 0, 0,
    0, 0, -12, 0, -4, 0, -6, -4,
    0, -9, -10, -12, -3, 0, -8, 0,
    -12, 0, 0, 0, 0, 33, 0, 0,
    2, 0, 0, -5, 0, 4, 0, -18,
    0, 0, 0, 0, 0, -39, -7, 14,
    12, -3, -17, 0, 4, -6, 0, -21,
    -2, -5, 4, -29, -4, 5, 0, 6,
    -15, -6, -15, -14, -17, 0, 0, -25,
    0, 24, 0, 0, -2, 0, 0, 0,
    -2, -2, -4, -11, -14, -1, -39, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, -4, -6, 0, 0,
    -8, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -8, 0, 0, 8,
    -1, 5, 0, -9, 4, -3, -1, -11,
    -4, 0, -5, -4, -3, 0, -6, -7,
    0, 0, -3, -1, -3, -7, -5, 0,
    0, -4, 0, 4, -3, 0, -9, 0,
    0, 0, -8, 0, -7, 0, -7, -7,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 4, 0, -6, 0, -3, -5,
    -13, -3, -3, -3, -1, -3, -5, -1,
    0, 0, 0, 0, 0, -4, -3, -3,
    0, 0, 0, 0, 5, -3, 0, -3,
    0, 0, 0, -3, -5, -3, -4, -5,
    -4, 0, 3, 17, -1, 0, -11, 0,
    -3, 8, 0, -4, -17, -5, 6, 0,
    0, -20, -7, 4, -7, 3, 0, -3,
    -3, -13, 0, -6, 2, 0, 0, -7,
    0, 0, 0, 4, 4, -8, -8, 0,
    -7, -4, -6, -4, -4, 0, -7, 2,
    -8, -7, 12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -5,
    0, 0, -4, -4, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -6, 0, -8, 0, 0, 0, -14, 0,
    3, -9, 8, 1, -3, -20, 0, 0,
    -9, -4, 0, -17, -10, -12, 0, 0,
    -18, -4, -17, -16, -20, 0, -11, 0,
    3, 28, -5, 0, -10, -4, -1, -4,
    -7, -11, -7, -15, -17, -10, -4, 0,
    0, -3, 0, 1, 0, 0, -29, -4,
    12, 9, -9, -15, 0, 1, -13, 0,
    -21, -3, -4, 8, -38, -5, 1, 0,
    0, -27, -5, -22, -4, -30, 0, 0,
    -29, 0, 25, 1, 0, -3, 0, 0,
    0, 0, -2, -3, -16, -3, 0, -27,
    0, 0, 0, 0, -13, 0, -4, 0,
    -1, -12, -20, 0, 0, -2, -6, -12,
    -4, 0, -3, 0, 0, 0, 0, -19,
    -4, -14, -13, -3, -7, -10, -4, -7,
    0, -8, -4, -14, -6, 0, -5, -8,
    -4, -8, 0, 2, 0, -3, -14, 0,
    8, 0, -7, 0, 0, 0, 0, 5,
    0, 3, -8, 17, 0, -4, -4, -5,
    0, 0, 0, 0, 0, 0, -12, 0,
    -4, 0, -6, -4, 0, -9, -10, -12,
    -3, 0, -8, 3, 17, 0, 0, 0,
    0, 33, 0, 0, 2, 0, 0, -5,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -3, -8, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -4, -4, 0, 0, -8,
    -4, 0, 0, -8, 0, 7, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 6, 8, 3, -4, 0, -13,
    -7, 0, 12, -14, -13, -8, -8, 17,
    7, 4, -36, -3, 8, -4, 0, -4,
    5, -4, -15, 0, -4, 4, -5, -3,
    -12, -3, 0, 0, 12, 8, 0, -12,
    0, -23, -5, 12, -5, -16, 1, -5,
    -14, -14, -4, 17, 4, 0, -6, 0,
    -11, 0, 3, 14, -10, -15, -17, -10,
    12, 0, 1, -30, -3, 4, -7, -3,
    -10, 0, -9, -15, -6, -6, -3, 0,
    0, -10, -9, -4, 0, 12, 10, -4,
    -23, 0, -23, -6, 0, -15, -24, -1,
    -13, -7, -14, -12, 11, 0, 0, -5,
    0, -8, -4, 0, -4, -7, 0, 7,
    -14, 4, 0, 0, -22, 0, -4, -9,
    -7, -3, -12, -10, -14, -10, 0, -12,
    -4, -10, -8, -12, -4, 0, 0, 1,
    20, -7, 0, -12, -4, 0, -4, -8,
    -10, -11, -12, -16, -5, -8, 8, 0,
    -6, 0, -21, -5, 2, 8, -13, -15,
    -8, -14, 14, -4, 2, -39, -7, 8,
    -9, -7, -15, 0, -12, -17, -5, -4,
    -3, -4, -9, -12, -1, 0, 0, 12,
    12, -3, -27, 0, -25, -10, 10, -16,
    -28, -8, -15, -17, -21, -14, 8, 0,
    0, 0, 0, -5, 0, 0, 4, -5,
    8, 3, -8, 8, 0, 0, -13, -1,
    0, -1, 0, 1, 1, -3, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 3, 12, 1, 0, -5, 0, 0,
    0, 0, -3, -3, -5, 0, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 16, 0, 7, 1, 1, -5,
    0, 8, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -25, 0, -4, 7, 0, 12,
    0, 0, 41, 5, -8, -8, 4, 4,
    -3, 1, -21, 0, 0, 20, -25, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -28, 16, 58, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, -8,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -11, 0,
    0, 1, 0, 0, 4, 54, -8, -3,
    13, 11, -11, 4, 0, 0, 4, 4,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -54, 12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, 0, 0, -11, 0, 0, 0, 0,
    -9, -2, 0, 0, 0, -9, 0, -5,
    0, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -28, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -8, 0, -6, 0,
    -11, 0, 0, 0, -7, 4, -5, 0,
    0, -11, -4, -10, 0, 0, -11, 0,
    -4, 0, -20, 0, -5, 0, 0, -34,
    -8, -17, -5, -15, 0, 0, -28, 0,
    -11, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -6, -7, -3, -7, 0, 0,
    0, 0, -9, 0, -9, 5, -5, 8,
    0, -3, -10, -3, -7, -8, 0, -5,
    -2, -3, 3, -11, -1, 0, 0, 0,
    -37, -3, -6, 0, -9, 0, -3, -20,
    -4, 0, 0, -3, -3, 0, 0, 0,
    0, 3, 0, -3, -7, -3, 7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, 0,
    0, -9, 0, -3, 0, 0, 0, -8,
    4, 0, 0, 0, -11, -4, -8, 0,
    0, -12, 0, -4, 0, -20, 0, 0,
    0, 0, -40, 0, -8, -15, -21, 0,
    0, -28, 0, -3, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -6, -2,
    -6, 1, 0, 0, 7, -5, 0, 13,
    20, -4, -4, -12, 5, 20, 7, 9,
    -11, 5, 17, 5, 12, 9, 11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 26, 20, -7, -4, 0, -3,
    33, 18, 33, 0, 0, 0, 4, 0,
    0, 15, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 6,
    0, 0, 0, 0, -35, -5, -3, -17,
    -20, 0, 0, -28, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    6, 0, 0, 0, 0, -35, -5, -3,
    -17, -20, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -10, 4, 0, -4,
    3, 7, 4, -12, 0, -1, -3, 4,
    0, 3, 0, 0, 0, 0, -10, 0,
    -4, -3, -8, 0, -4, -17, 0, 26,
    -4, 0, -9, -3, 0, -3, -7, 0,
    -4, -12, -8, -5, 0, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 6, 0, 0, 0, 0, -35,
    -5, -3, -17, -20, 0, 0, -28, 0,
    0, 0, 0, 0, 0, 21, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, -13, -5, -4, 12, -4, -4,
    -17, 1, -2, 1, -3, -11, 1, 9,
    1, 3, 1, 3, -10, -17, -5, 0,
    -16, -8, -11, -17, -16, 0, -7, -8,
    -5, -5, -3, -3, -5, -3, 0, -3,
    -1, 6, 0, 6, -3, 0, 13, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -4, -4, 0, 0,
    -11, 0, -2, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -25, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, 0, 0, 0, -3, 0, 0, -7,
    -4, 4, 0, -7, -8, -3, 0, -12,
    -3, -9, -3, -5, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -28, 0, 13, 0, 0, -7, 0,
    0, 0, 0, -5, 0, -4, 0, 0,
    -2, 0, 0, -3, 0, -10, 0, 0,
    17, -5, -14, -13, 3, 5, 5, -1,
    -12, 3, 6, 3, 12, 3, 14, -3,
    -11, 0, 0, -17, 0, 0, -12, -11,
    0, 0, -8, 0, -5, -7, 0, -6,
    0, -6, 0, -3, 6, 0, -3, -12,
    -4, 15, 0, 0, -4, 0, -8, 0,
    0, 5, -10, 0, 4, -4, 3, 0,
    0, -14, 0, -3, -1, 0, -4, 5,
    -3, 0, 0, 0, -17, -5, -9, 0,
    -12, 0, 0, -20, 0, 15, -4, 0,
    -7, 0, 2, 0, -4, 0, -4, -12,
    0, -4, 4, 0, 0, 0, 0, -3,
    0, 0, 4, -5, 1, 0, 0, -5,
    -3, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -26, 0, 9, 0,
    0, -3, 0, 0, 0, 0, 1, 0,
    -4, -4, 0, 0, 0, 8, 0, 10,
    0, 0, 0, 0, 0, -26, -24, 1,
    18, 12, 7, -17, 3, 17, 0, 15,
    0, 8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_26 = {
#else
lv_font_t lv_font_montserrat_26 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 29,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRAT_26*/

