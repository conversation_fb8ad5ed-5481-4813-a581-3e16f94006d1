/*
 * Copyright 2025 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */
/*******************************************************************************
 * Size: 23 px
 * Bpp: 4
 * Opts: --user-data-dir=C:\Users\<USER>\AppData\Roaming\gui-guider --app-path=D:\GUI_Guider\Gui-Guider\resources\app.asar --no-sandbox --no-zygote --lang=zh-CN --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1755411396113839 --launch-time-ticks=857559356 --mojo-platform-channel-handle=2860 --field-trial-handle=1716,i,5431673224908323737,3035983149899318021,131072 --disable-features=SpareRendererForSitePerProcess,WinRetrieveSuggestionsOnlyOnDemand /prefetch:1
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_23
#define LV_FONT_MONTSERRATMEDIUM_23 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_23

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2f, 0xf5, 0x2f, 0xf5, 0x1f, 0xf4, 0x1f, 0xf3,
    0xf, 0xf3, 0xf, 0xf2, 0xf, 0xf1, 0xe, 0xf1,
    0xe, 0xf0, 0xd, 0xf0, 0x9, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xc2, 0x5f, 0xf8, 0x1d, 0xe3,

    /* U+0022 "\"" */
    0x8f, 0x50, 0x6f, 0x88, 0xf5, 0x5, 0xf8, 0x7f,
    0x40, 0x5f, 0x77, 0xf4, 0x5, 0xf7, 0x7f, 0x40,
    0x4f, 0x66, 0xf3, 0x4, 0xf6, 0x0, 0x0, 0x0,
    0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0xc, 0xd0, 0x0, 0xd, 0xd0, 0x0,
    0x0, 0x0, 0xe, 0xb0, 0x0, 0xf, 0xb0, 0x0,
    0x0, 0x0, 0xf, 0x90, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x2f, 0x70, 0x0, 0x3f, 0x70, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x6, 0xaa, 0xcf, 0xba, 0xaa, 0xdf, 0xba, 0xa5,
    0x0, 0x0, 0x8f, 0x10, 0x0, 0x9f, 0x10, 0x0,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x0, 0x0, 0xcd, 0x0, 0x0, 0xcd, 0x0, 0x0,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0xeb, 0x0, 0x0,
    0x3a, 0xaa, 0xfe, 0xaa, 0xaa, 0xfd, 0xaa, 0x80,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x4, 0xf5, 0x0, 0x4, 0xf5, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0x0, 0x6, 0xf3, 0x0, 0x0,
    0x0, 0x8, 0xf1, 0x0, 0x8, 0xf1, 0x0, 0x0,
    0x0, 0xa, 0xf0, 0x0, 0xa, 0xf0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9d,
    0xff, 0xfd, 0xa4, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x2, 0xff, 0xa2, 0x7f, 0x2,
    0x7e, 0x20, 0x8, 0xfd, 0x0, 0x7f, 0x0, 0x0,
    0x0, 0xa, 0xfa, 0x0, 0x7f, 0x0, 0x0, 0x0,
    0x8, 0xfe, 0x20, 0x7f, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf9, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xff, 0xff, 0xb7, 0x10, 0x0, 0x0, 0x0, 0x5a,
    0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x7d, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x7f, 0xf2, 0x0, 0x0, 0x0, 0x7f, 0x0, 0xf,
    0xf4, 0x3, 0x20, 0x0, 0x7f, 0x0, 0x2f, 0xf3,
    0xc, 0xf9, 0x30, 0x7f, 0x4, 0xdf, 0xd0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x17,
    0xce, 0xff, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x5d, 0xfe, 0x70, 0x0, 0x0, 0x1, 0xeb,
    0x0, 0x0, 0x6f, 0xa5, 0x8f, 0x80, 0x0, 0x0,
    0xbe, 0x10, 0x0, 0xe, 0xb0, 0x0, 0x8f, 0x10,
    0x0, 0x6f, 0x50, 0x0, 0x1, 0xf6, 0x0, 0x3,
    0xf4, 0x0, 0x2f, 0xa0, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0x3f, 0x40, 0xc, 0xe1, 0x0, 0x0, 0x0,
    0xf9, 0x0, 0x6, 0xf2, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0x8, 0xf6, 0x14, 0xeb, 0x2, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xfb, 0x10, 0xdd,
    0x3, 0xcf, 0xf9, 0x0, 0x0, 0x1, 0x31, 0x0,
    0x8f, 0x32, 0xfb, 0x46, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0x9e, 0x0, 0x6, 0xf3, 0x0,
    0x0, 0x0, 0xd, 0xd0, 0xd, 0xa0, 0x0, 0x1f,
    0x70, 0x0, 0x0, 0x9, 0xf3, 0x0, 0xe8, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x4, 0xf7, 0x0, 0xd,
    0xa0, 0x0, 0xf, 0x60, 0x0, 0x1, 0xec, 0x0,
    0x0, 0x9e, 0x0, 0x5, 0xf2, 0x0, 0x0, 0xaf,
    0x20, 0x0, 0x2, 0xfa, 0x35, 0xea, 0x0, 0x0,
    0x5f, 0x60, 0x0, 0x0, 0x3, 0xcf, 0xe8, 0x0,

    /* U+0026 "&" */
    0x0, 0x3, 0xae, 0xfd, 0x81, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xb9, 0xdf, 0xc0, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0xcf, 0x30, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0xdf,
    0x50, 0x1, 0xef, 0x10, 0x0, 0x0, 0x5, 0xfe,
    0x25, 0xef, 0x70, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x3, 0xef, 0xba, 0xfe,
    0x20, 0x1, 0x61, 0x2, 0xff, 0x60, 0x9, 0xfe,
    0x30, 0x6f, 0x80, 0xaf, 0x80, 0x0, 0x9, 0xfe,
    0x3c, 0xf3, 0xe, 0xf4, 0x0, 0x0, 0x8, 0xff,
    0xfc, 0x0, 0xef, 0x60, 0x0, 0x0, 0x9, 0xff,
    0x70, 0x8, 0xff, 0x50, 0x0, 0x6, 0xef, 0xff,
    0x40, 0xb, 0xff, 0xfd, 0xdf, 0xff, 0x67, 0xff,
    0x30, 0x5, 0xbe, 0xfe, 0xc8, 0x10, 0x6, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x8f, 0x58, 0xf5, 0x7f, 0x47, 0xf4, 0x7f, 0x46,
    0xf3, 0x0, 0x0,

    /* U+0028 "(" */
    0x0, 0x5f, 0xc0, 0x0, 0xdf, 0x40, 0x5, 0xfc,
    0x0, 0xb, 0xf7, 0x0, 0xf, 0xf2, 0x0, 0x4f,
    0xe0, 0x0, 0x7f, 0xb0, 0x0, 0x9f, 0x80, 0x0,
    0xbf, 0x70, 0x0, 0xcf, 0x60, 0x0, 0xcf, 0x50,
    0x0, 0xcf, 0x60, 0x0, 0xbf, 0x70, 0x0, 0x9f,
    0x80, 0x0, 0x7f, 0xb0, 0x0, 0x4f, 0xe0, 0x0,
    0xf, 0xf2, 0x0, 0xb, 0xf7, 0x0, 0x5, 0xfc,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x5f, 0xc0,

    /* U+0029 ")" */
    0x1f, 0xf1, 0x0, 0x8, 0xfa, 0x0, 0x1, 0xff,
    0x10, 0x0, 0xbf, 0x70, 0x0, 0x6f, 0xc0, 0x0,
    0x2f, 0xf0, 0x0, 0xf, 0xf3, 0x0, 0xc, 0xf6,
    0x0, 0xb, 0xf7, 0x0, 0xa, 0xf8, 0x0, 0x9,
    0xf9, 0x0, 0xa, 0xf8, 0x0, 0xb, 0xf7, 0x0,
    0xc, 0xf6, 0x0, 0xf, 0xf3, 0x0, 0x2f, 0xf0,
    0x0, 0x6f, 0xc0, 0x0, 0xbf, 0x70, 0x1, 0xff,
    0x10, 0x8, 0xfa, 0x0, 0x1f, 0xf1, 0x0,

    /* U+002A "*" */
    0x0, 0x1, 0xf4, 0x0, 0x0, 0x50, 0x1f, 0x40,
    0x41, 0x5f, 0xc4, 0xf6, 0xaf, 0x80, 0x4c, 0xff,
    0xfe, 0x60, 0x0, 0x7f, 0xff, 0x91, 0x3, 0xdf,
    0xaf, 0xaf, 0xe5, 0x2b, 0x21, 0xf4, 0x19, 0x40,
    0x0, 0x1f, 0x40, 0x0, 0x0, 0x0, 0x61, 0x0,
    0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x3d, 0x80, 0x0, 0x0, 0x0, 0x4,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x5c, 0xcc,
    0xdf, 0xec, 0xcc, 0xa7, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0, 0x0,
    0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0,

    /* U+002C "," */
    0x3, 0x0, 0x9f, 0xd0, 0xdf, 0xf1, 0x3f, 0xe0,
    0x2f, 0x80, 0x6f, 0x30, 0xad, 0x0,

    /* U+002D "-" */
    0x9d, 0xdd, 0xdd, 0x6b, 0xff, 0xff, 0xf8,

    /* U+002E "." */
    0x0, 0x0, 0x8f, 0xb0, 0xef, 0xf1, 0x7f, 0xa0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xf6, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0,
    0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0, 0x0, 0xaf,
    0x50, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x6, 0xf9, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0,
    0x3, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xf6, 0x0,
    0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x4f,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x2, 0x9d, 0xfe, 0xb4, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x4, 0xff,
    0xa3, 0x1, 0x7f, 0xf9, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x5, 0xff, 0x30, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x5f, 0xe0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x1f, 0xf3,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0xcf,
    0x80, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x5f, 0xe0, 0x4f, 0xf1, 0x0,
    0x0, 0x0, 0xbf, 0xa0, 0xe, 0xf9, 0x0, 0x0,
    0x4, 0xff, 0x30, 0x4, 0xff, 0xa2, 0x1, 0x7f,
    0xf9, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x2, 0x9d, 0xfe, 0xb4, 0x0, 0x0,

    /* U+0031 "1" */
    0xdf, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xf1, 0x0,
    0x3, 0xff, 0x10, 0x0, 0x3f, 0xf1, 0x0, 0x3,
    0xff, 0x10, 0x0, 0x3f, 0xf1, 0x0, 0x3, 0xff,
    0x10, 0x0, 0x3f, 0xf1, 0x0, 0x3, 0xff, 0x10,
    0x0, 0x3f, 0xf1, 0x0, 0x3, 0xff, 0x10, 0x0,
    0x3f, 0xf1, 0x0, 0x3, 0xff, 0x10, 0x0, 0x3f,
    0xf1, 0x0, 0x3, 0xff, 0x10, 0x0, 0x3f, 0xf1,

    /* U+0032 "2" */
    0x0, 0x39, 0xde, 0xfe, 0xa4, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x5f, 0xf8, 0x20,
    0x3, 0xbf, 0xf4, 0x0, 0x63, 0x0, 0x0, 0x0,
    0xdf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1d, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x2e, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x2e,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x62, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,

    /* U+0033 "3" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x37, 0x79,
    0xef, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1, 0x6, 0x0,
    0x0, 0x0, 0x8, 0xfe, 0x8, 0xfd, 0x62, 0x0,
    0x29, 0xff, 0x70, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x6, 0xae, 0xff, 0xda, 0x40, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0x80, 0x0, 0x66, 0x0, 0x0,
    0x0, 0xc, 0xfb, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x9f, 0xd1, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x6, 0xff, 0x30, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x1f, 0xff, 0xee, 0xee, 0xee, 0xff, 0xee, 0xe1,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xed, 0xa5, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x13,
    0x8f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x7, 0x10,
    0x0, 0x0, 0x5, 0xff, 0x24, 0xfe, 0x83, 0x0,
    0x27, 0xff, 0xb0, 0x3d, 0xff, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x4, 0x9d, 0xff, 0xeb, 0x60, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x5b, 0xdf, 0xec, 0x92, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0x60, 0x1, 0xef, 0xd5,
    0x10, 0x2, 0x70, 0x0, 0xbf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x70, 0x5a, 0xcc, 0xa5, 0x0, 0xd, 0xf7, 0xcf,
    0xff, 0xff, 0xfc, 0x10, 0xdf, 0xff, 0x92, 0x1,
    0x6e, 0xfc, 0xc, 0xff, 0x80, 0x0, 0x0, 0x3f,
    0xf4, 0xaf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0x76,
    0xff, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x1f, 0xf5,
    0x0, 0x0, 0x1, 0xff, 0x40, 0x7f, 0xf6, 0x0,
    0x3, 0xcf, 0xd0, 0x0, 0x8f, 0xff, 0xde, 0xff,
    0xd2, 0x0, 0x0, 0x3a, 0xef, 0xfc, 0x70, 0x0,

    /* U+0037 "7" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x5f, 0xe0, 0x0,
    0x0, 0x0, 0xef, 0x65, 0xfe, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x4d, 0xb0, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x17, 0xce, 0xfe, 0xb6, 0x0, 0x0, 0x3e,
    0xff, 0xed, 0xff, 0xfd, 0x10, 0xe, 0xfc, 0x30,
    0x0, 0x4e, 0xfb, 0x4, 0xff, 0x10, 0x0, 0x0,
    0x4f, 0xf1, 0x5f, 0xe0, 0x0, 0x0, 0x1, 0xff,
    0x23, 0xff, 0x20, 0x0, 0x0, 0x5f, 0xf0, 0xb,
    0xfe, 0x51, 0x2, 0x7f, 0xf7, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x7, 0xff, 0xfc, 0xbd,
    0xff, 0xe4, 0x6, 0xff, 0x60, 0x0, 0x1, 0x8f,
    0xf3, 0xdf, 0x80, 0x0, 0x0, 0x0, 0xbf, 0xaf,
    0xf5, 0x0, 0x0, 0x0, 0x8, 0xfc, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0xcf, 0xa6, 0xff, 0x81, 0x0,
    0x2, 0xaf, 0xf3, 0x8, 0xff, 0xfe, 0xde, 0xff,
    0xf6, 0x0, 0x3, 0x9d, 0xef, 0xec, 0x81, 0x0,

    /* U+0039 "9" */
    0x0, 0x5, 0xbe, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xde, 0xff, 0xb0, 0x0, 0xa, 0xfe,
    0x40, 0x0, 0x4e, 0xfa, 0x0, 0x1f, 0xf4, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x4f, 0xf0, 0x0, 0x0,
    0x0, 0xcf, 0xa0, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0xdf, 0xd0, 0x1f, 0xf5, 0x0, 0x0, 0x4, 0xff,
    0xf0, 0xa, 0xff, 0x72, 0x1, 0x7f, 0xff, 0xf1,
    0x0, 0xbf, 0xff, 0xff, 0xfe, 0x6f, 0xf0, 0x0,
    0x4, 0x9c, 0xca, 0x60, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfe, 0x0, 0x0, 0x73, 0x0, 0x4, 0xbf,
    0xf4, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x1, 0x8c, 0xef, 0xec, 0x61, 0x0, 0x0,

    /* U+003A ":" */
    0x7f, 0xa0, 0xef, 0xf1, 0x8f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xb0, 0xef, 0xf1, 0x7f, 0xa0,

    /* U+003B ";" */
    0x7f, 0xa0, 0xef, 0xf1, 0x8f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xa0, 0xdf, 0xf1, 0x6f, 0xf0,
    0x1f, 0x90, 0x5f, 0x40, 0x9e, 0x0, 0x32, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x80, 0x0, 0x0,
    0x1, 0x7d, 0xfd, 0x0, 0x0, 0x5b, 0xff, 0xe9,
    0x30, 0x29, 0xef, 0xfb, 0x50, 0x0, 0x6f, 0xfd,
    0x71, 0x0, 0x0, 0x7, 0xff, 0x82, 0x0, 0x0,
    0x0, 0x17, 0xdf, 0xfc, 0x61, 0x0, 0x0, 0x0,
    0x4a, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x1, 0x6c,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2, 0x9c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xd5, 0xcc, 0xcc,
    0xcc, 0xcc, 0xca, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcc, 0xcc, 0xcc, 0xcc,
    0xca, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xd0,

    /* U+003E ">" */
    0x56, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x17, 0xdf, 0xfd, 0x71, 0x0,
    0x0, 0x0, 0x39, 0xef, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x6d,
    0xfd, 0x0, 0x0, 0x4a, 0xff, 0xfa, 0x30, 0x28,
    0xef, 0xfc, 0x60, 0x0, 0x6f, 0xfe, 0x92, 0x0,
    0x0, 0x7, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x39, 0xdf, 0xfe, 0xa4, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x7f, 0xe7, 0x10, 0x2,
    0xbf, 0xf4, 0x6, 0x20, 0x0, 0x0, 0xe, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x2d, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xb0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xa, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xd5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x38, 0xce, 0xff, 0xec, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xff, 0xb9,
    0x77, 0x9c, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x60, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60,
    0x0, 0x0, 0x8f, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xf5, 0x0, 0x4, 0xfc, 0x0, 0x2,
    0xae, 0xfe, 0x92, 0x5f, 0xa0, 0xcf, 0x20, 0xd,
    0xe1, 0x0, 0x6f, 0xfe, 0xcd, 0xff, 0x9f, 0xa0,
    0x1f, 0xb0, 0x4f, 0x70, 0x3, 0xff, 0x70, 0x0,
    0x2c, 0xff, 0xa0, 0x8, 0xf1, 0x9f, 0x10, 0xb,
    0xf8, 0x0, 0x0, 0x1, 0xef, 0xa0, 0x3, 0xf6,
    0xce, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x8f,
    0xa0, 0x0, 0xf9, 0xdc, 0x0, 0x2f, 0xe0, 0x0,
    0x0, 0x0, 0x5f, 0xa0, 0x0, 0xea, 0xdc, 0x0,
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0,
    0xfa, 0xce, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x8f, 0xa0, 0x0, 0xf8, 0x9f, 0x20, 0xa, 0xf8,
    0x0, 0x0, 0x1, 0xef, 0xa0, 0x4, 0xf5, 0x4f,
    0x70, 0x3, 0xff, 0x60, 0x0, 0x2c, 0xff, 0xd0,
    0xb, 0xf1, 0xd, 0xe1, 0x0, 0x5f, 0xfe, 0xbc,
    0xff, 0x4e, 0xfd, 0xdf, 0x70, 0x4, 0xfc, 0x0,
    0x2, 0xae, 0xfe, 0x92, 0x3, 0xcf, 0xd6, 0x0,
    0x0, 0x8f, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0xff, 0xb9, 0x88, 0xad, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xce, 0xff, 0xeb,
    0x71, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0xef, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xd2,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x5, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0xdf, 0x60,
    0x0, 0x0, 0x1, 0xff, 0x30, 0x0, 0x0, 0x7f,
    0xd0, 0x0, 0x0, 0x8, 0xff, 0xcc, 0xcc, 0xcc,
    0xdf, 0xf5, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x6f, 0xe0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x40, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa,

    /* U+0042 "B" */
    0x9f, 0xff, 0xff, 0xff, 0xfd, 0x92, 0x0, 0x9,
    0xff, 0xdd, 0xdd, 0xde, 0xff, 0xf4, 0x0, 0x9f,
    0xb0, 0x0, 0x0, 0x3, 0xcf, 0xf1, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0xf, 0xf5, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x30, 0x9f, 0xb0, 0x0,
    0x0, 0x3, 0xcf, 0xb0, 0x9, 0xff, 0xdd, 0xdd,
    0xdf, 0xff, 0xb1, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x27, 0xff, 0x80, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x19, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x29, 0xfb, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xd0, 0x9f, 0xfd, 0xdd, 0xdd, 0xdf, 0xff, 0xe3,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xec, 0x71, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xc8, 0x20, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x9f, 0xfc, 0x62, 0x1, 0x49, 0xff, 0x70, 0x7f,
    0xf8, 0x0, 0x0, 0x0, 0x3, 0x90, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x39, 0x0, 0xa, 0xff, 0xc6, 0x10, 0x3, 0x9f,
    0xf7, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x1, 0x7c, 0xef, 0xec, 0x82, 0x0,

    /* U+0044 "D" */
    0x9f, 0xff, 0xff, 0xff, 0xec, 0x71, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x15, 0xbf, 0xfa, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf2,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf2,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x80,
    0x9f, 0xb0, 0x0, 0x0, 0x15, 0xcf, 0xfb, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xdb, 0x61, 0x0, 0x0,

    /* U+0045 "E" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xee,
    0xee, 0xee, 0xeb, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,

    /* U+0046 "F" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x9f, 0xfe,
    0xee, 0xee, 0xee, 0xb0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x17, 0xbe, 0xfe, 0xd9, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xb1, 0x0,
    0x9f, 0xfd, 0x62, 0x0, 0x38, 0xff, 0xa0, 0x7f,
    0xf8, 0x0, 0x0, 0x0, 0x1, 0x90, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0xdf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xcb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xc1, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x7, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0xa, 0xff, 0xc6, 0x20, 0x13, 0x8e,
    0xfc, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0x1, 0x7c, 0xef, 0xfd, 0x94, 0x0,

    /* U+0048 "H" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x49,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x49, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x49, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x49, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x49, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x49, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x49, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x49, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4,

    /* U+0049 "I" */
    0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9,
    0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f,
    0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb,

    /* U+004A "J" */
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x80, 0x8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x80, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0xc,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x80, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x80, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x2, 0x50, 0x0, 0x1, 0xff, 0x50,
    0xef, 0x81, 0x1, 0xbf, 0xf0, 0x7, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x3, 0xae, 0xfe, 0xb3, 0x0,

    /* U+004B "K" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x2e, 0xfa, 0x0, 0x9f,
    0xb0, 0x0, 0x0, 0x1d, 0xfa, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x1d, 0xfb, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x1d, 0xfc, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x1d,
    0xfc, 0x10, 0x0, 0x0, 0x9, 0xfb, 0x1c, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x9f, 0xcc, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x9, 0xff, 0xfe, 0x3d, 0xff,
    0x20, 0x0, 0x0, 0x9f, 0xfe, 0x20, 0x1e, 0xfd,
    0x10, 0x0, 0x9, 0xfe, 0x20, 0x0, 0x2f, 0xfb,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x4f, 0xf9,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0xf4,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,

    /* U+004C "L" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+004D "M" */
    0x9f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf8, 0x9f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf8, 0x9f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf8, 0x9f, 0xef, 0xe1, 0x0,
    0x0, 0x0, 0x1e, 0xfe, 0xf8, 0x9f, 0xab, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0x9a, 0xf8, 0x9f, 0xa2,
    0xff, 0x30, 0x0, 0x3, 0xfe, 0x1a, 0xf8, 0x9f,
    0xa0, 0x8f, 0xc0, 0x0, 0xc, 0xf6, 0xa, 0xf8,
    0x9f, 0xa0, 0xe, 0xf5, 0x0, 0x5f, 0xd0, 0xa,
    0xf8, 0x9f, 0xa0, 0x5, 0xfe, 0x0, 0xef, 0x30,
    0xa, 0xf8, 0x9f, 0xa0, 0x0, 0xbf, 0x98, 0xfa,
    0x0, 0xa, 0xf8, 0x9f, 0xa0, 0x0, 0x2f, 0xff,
    0xf1, 0x0, 0xa, 0xf8, 0x9f, 0xa0, 0x0, 0x8,
    0xff, 0x60, 0x0, 0xa, 0xf8, 0x9f, 0xa0, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0xa, 0xf8, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x9f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8,

    /* U+004E "N" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x49,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x9f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0xff, 0x49, 0xff,
    0xff, 0x20, 0x0, 0x0, 0xf, 0xf4, 0x9f, 0xcd,
    0xfd, 0x10, 0x0, 0x0, 0xff, 0x49, 0xfb, 0x2f,
    0xfb, 0x0, 0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x5f,
    0xf8, 0x0, 0x0, 0xff, 0x49, 0xfb, 0x0, 0x8f,
    0xf5, 0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x0, 0xbf,
    0xf3, 0x0, 0xff, 0x49, 0xfb, 0x0, 0x0, 0xdf,
    0xe1, 0xf, 0xf4, 0x9f, 0xb0, 0x0, 0x2, 0xef,
    0xc0, 0xff, 0x49, 0xfb, 0x0, 0x0, 0x4, 0xff,
    0xaf, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0x49, 0xfb, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x49, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xf4,

    /* U+004F "O" */
    0x0, 0x0, 0x17, 0xce, 0xff, 0xd9, 0x30, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x9f, 0xfc, 0x62, 0x1, 0x4a,
    0xff, 0xd1, 0x0, 0x7, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x4e, 0xfc, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd0, 0xbf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0xdf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf1, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xd0, 0x1f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x60, 0x7, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x3e, 0xfc, 0x0, 0x0, 0x9f, 0xfc,
    0x51, 0x1, 0x4a, 0xff, 0xd1, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x17, 0xce, 0xff, 0xd9, 0x30, 0x0, 0x0,

    /* U+0050 "P" */
    0x9f, 0xff, 0xff, 0xff, 0xd9, 0x30, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x39, 0xff, 0x80, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x7f, 0xf1, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0xe, 0xf6, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x9f, 0xb0, 0x0, 0x0, 0x29, 0xff, 0x80, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xd9, 0x30, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x17, 0xbe, 0xff, 0xd9, 0x30, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x9f, 0xfc, 0x62, 0x1, 0x4a,
    0xff, 0xd1, 0x0, 0x6, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x4e, 0xfc, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0xbf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf1, 0xdf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf2,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xc0, 0x2f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x8, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x3e, 0xfd, 0x0, 0x0, 0xbf, 0xfb,
    0x40, 0x0, 0x39, 0xff, 0xe2, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x29, 0xdf, 0xff, 0xfa, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x10, 0x1,
    0x95, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfd,
    0xdf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xcf, 0xfc, 0x50,

    /* U+0052 "R" */
    0x9f, 0xff, 0xff, 0xff, 0xd9, 0x30, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x39, 0xff, 0x80, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x8f, 0xf1, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0xe, 0xf6, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x9f, 0xb0, 0x0, 0x0, 0x28, 0xff, 0x80, 0x9f,
    0xfe, 0xee, 0xef, 0xff, 0xf9, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0xdf, 0x90, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x3f, 0xf4, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x8,
    0xfe, 0x10, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0xdf,
    0xa0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0xf5,

    /* U+0053 "S" */
    0x0, 0x1, 0x8c, 0xef, 0xec, 0x93, 0x0, 0x0,
    0x5f, 0xff, 0xfe, 0xff, 0xff, 0x70, 0x2, 0xff,
    0xb3, 0x0, 0x2, 0x8e, 0x30, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xe8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xff, 0xff, 0xb7, 0x10, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x6c, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf4, 0x4, 0x30, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0xc, 0xfa, 0x40, 0x0, 0x4,
    0xdf, 0xc0, 0x7, 0xff, 0xff, 0xfe, 0xff, 0xfd,
    0x10, 0x0, 0x6, 0xad, 0xff, 0xeb, 0x60, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x5, 0xfe, 0xbf,
    0x90, 0x0, 0x0, 0x0, 0x5, 0xfe, 0xbf, 0x90,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0xbf, 0x90, 0x0,
    0x0, 0x0, 0x5, 0xfe, 0xbf, 0x90, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0xbf, 0x90, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x5,
    0xfe, 0xbf, 0x90, 0x0, 0x0, 0x0, 0x5, 0xfe,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x5, 0xfe, 0xaf,
    0x90, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x7, 0xfc, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x1f, 0xf8, 0x0, 0x0,
    0x0, 0x5f, 0xf3, 0x7, 0xff, 0xa3, 0x0, 0x28,
    0xff, 0xa0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xea, 0x40, 0x0,

    /* U+0056 "V" */
    0xc, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0x5, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0xef, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x40, 0x0, 0x7f, 0xf0, 0x0, 0x0,
    0x0, 0x7, 0xfd, 0x0, 0x0, 0x1f, 0xf7, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x9, 0xfd,
    0x0, 0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x2,
    0xff, 0x50, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0x3, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0xb, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xfa, 0x0, 0x2f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x9f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x81,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe8, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x1f, 0xf4, 0x0, 0x0, 0x0, 0x3, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0xc, 0xf9, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x8f, 0xa0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0xdf, 0x50, 0x1,
    0xff, 0x40, 0x0, 0x0, 0x3f, 0xde, 0xf4, 0x0,
    0x0, 0x3, 0xff, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x9f, 0x89, 0xf9, 0x0, 0x0, 0x8, 0xfa,
    0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0xef, 0x24,
    0xfe, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x1f,
    0xf4, 0x0, 0x4, 0xfd, 0x0, 0xef, 0x40, 0x0,
    0x3f, 0xf0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0xa,
    0xf7, 0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0xf, 0xf2, 0x0, 0x3f,
    0xf0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x1, 0xff,
    0x40, 0x5f, 0xc0, 0x0, 0xd, 0xf5, 0x4, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0xaf, 0x70,
    0x0, 0x8, 0xfa, 0x9, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xe1, 0xff, 0x20, 0x0, 0x3, 0xff,
    0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xfc, 0x0, 0x0, 0x0, 0xdf, 0x9f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x1e, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x3, 0xff, 0x60, 0x0, 0x0, 0xc, 0xfa, 0x0,
    0x0, 0x8f, 0xf2, 0x0, 0x0, 0x8f, 0xd0, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x4, 0xff, 0x30, 0x0,
    0x0, 0x1, 0xef, 0x90, 0x1e, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf5, 0xbf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfb, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xd0, 0x7f, 0xf2, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x30, 0xb, 0xfd, 0x0, 0x0,
    0x0, 0x2f, 0xf7, 0x0, 0x1, 0xef, 0x90, 0x0,
    0x0, 0xdf, 0xb0, 0x0, 0x0, 0x4f, 0xf5, 0x0,
    0x9, 0xfe, 0x10, 0x0, 0x0, 0x9, 0xff, 0x20,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0,

    /* U+0059 "Y" */
    0xc, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfa,
    0x3, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0x0, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0xe, 0xf8, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x5, 0xff, 0x20, 0x0, 0x1f, 0xf3, 0x0,
    0x0, 0x0, 0xbf, 0xb0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0x4, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x1d, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xef, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+005B "[" */
    0x9f, 0xff, 0xf3, 0x9f, 0xec, 0xc2, 0x9f, 0xa0,
    0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f,
    0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0,
    0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0,
    0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f,
    0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0,
    0x9f, 0xa0, 0x0, 0x9f, 0xa0, 0x0, 0x9f, 0xa0,
    0x0, 0x9f, 0xec, 0xc2, 0x9f, 0xff, 0xf3,

    /* U+005C "\\" */
    0xbf, 0x40, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x9,
    0xf6, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xf3, 0x0, 0x0, 0x0, 0x7, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x5f, 0xa0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xa, 0xf5, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0,
    0x0, 0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x7,
    0xf8,

    /* U+005D "]" */
    0x9f, 0xff, 0xf4, 0x7c, 0xcf, 0xf4, 0x0, 0xf,
    0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0,
    0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4,
    0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf,
    0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0,
    0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4,
    0x0, 0xf, 0xf4, 0x0, 0xf, 0xf4, 0x0, 0xf,
    0xf4, 0x6c, 0xcf, 0xf4, 0x9f, 0xff, 0xf4,

    /* U+005E "^" */
    0x0, 0x0, 0x28, 0x50, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x10, 0x0, 0x0, 0x1, 0xfc, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0x3c, 0xe0, 0x0, 0x0, 0xe,
    0xc0, 0x5f, 0x50, 0x0, 0x5, 0xf6, 0x0, 0xec,
    0x0, 0x0, 0xce, 0x0, 0x8, 0xf3, 0x0, 0x3f,
    0x80, 0x0, 0x2f, 0x90, 0xa, 0xf2, 0x0, 0x0,
    0xbf, 0x11, 0xfb, 0x0, 0x0, 0x4, 0xf7,

    /* U+005F "_" */
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x42,

    /* U+0060 "`" */
    0xa, 0xfe, 0x20, 0x0, 0x6, 0xfe, 0x20, 0x0,
    0x3, 0xee, 0x20,

    /* U+0061 "a" */
    0x1, 0x7c, 0xef, 0xeb, 0x50, 0x3, 0xff, 0xff,
    0xef, 0xff, 0xa0, 0xd, 0x72, 0x0, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xb0, 0x2a, 0xdf, 0xff, 0xff,
    0xfc, 0x3f, 0xfd, 0x98, 0x88, 0xcf, 0xca, 0xfa,
    0x0, 0x0, 0x7, 0xfc, 0xdf, 0x60, 0x0, 0x0,
    0x9f, 0xca, 0xfb, 0x0, 0x0, 0x5f, 0xfc, 0x2f,
    0xfd, 0x99, 0xcf, 0xdf, 0xc0, 0x19, 0xdf, 0xec,
    0x55, 0xfc,

    /* U+0062 "b" */
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0x29, 0xef, 0xeb, 0x40, 0x0, 0xef,
    0x9f, 0xff, 0xef, 0xff, 0xa0, 0xe, 0xff, 0xe5,
    0x0, 0x17, 0xff, 0xa0, 0xef, 0xf2, 0x0, 0x0,
    0x5, 0xff, 0x2e, 0xf8, 0x0, 0x0, 0x0, 0xc,
    0xf8, 0xef, 0x50, 0x0, 0x0, 0x0, 0x9f, 0xae,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0xfa, 0xef, 0x80,
    0x0, 0x0, 0x0, 0xcf, 0x8e, 0xff, 0x20, 0x0,
    0x0, 0x5f, 0xf2, 0xef, 0xfe, 0x50, 0x1, 0x7f,
    0xfa, 0xe, 0xf8, 0xff, 0xfe, 0xff, 0xfa, 0x0,
    0xef, 0x32, 0xae, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x6b, 0xef, 0xea, 0x30, 0x0, 0x2,
    0xcf, 0xff, 0xef, 0xff, 0x70, 0x0, 0xdf, 0xd5,
    0x0, 0x17, 0xff, 0x20, 0x7f, 0xe1, 0x0, 0x0,
    0x4, 0x10, 0xd, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x10, 0x0,
    0x0, 0x41, 0x0, 0xd, 0xfd, 0x40, 0x0, 0x7f,
    0xf2, 0x0, 0x1c, 0xff, 0xfe, 0xff, 0xf7, 0x0,
    0x0, 0x6, 0xbe, 0xfe, 0xa3, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x7c, 0xef, 0xd8,
    0x9, 0xf9, 0x0, 0x2d, 0xff, 0xfe, 0xff, 0xdb,
    0xf9, 0x1, 0xef, 0xd4, 0x0, 0x18, 0xff, 0xf9,
    0x8, 0xfe, 0x10, 0x0, 0x0, 0x6f, 0xf9, 0xd,
    0xf7, 0x0, 0x0, 0x0, 0xe, 0xf9, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0xf, 0xf4, 0x0,
    0x0, 0x0, 0xa, 0xf9, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0xd, 0xf9, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x5f, 0xf9, 0x1, 0xef, 0xc2, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0x2d, 0xff, 0xdc, 0xef, 0xea, 0xf9,
    0x0, 0x0, 0x7c, 0xef, 0xd8, 0x18, 0xf9,

    /* U+0065 "e" */
    0x0, 0x0, 0x7c, 0xef, 0xd8, 0x10, 0x0, 0x0,
    0x2d, 0xff, 0xee, 0xff, 0xe4, 0x0, 0x1, 0xdf,
    0xb2, 0x0, 0x19, 0xfe, 0x20, 0x7, 0xfc, 0x0,
    0x0, 0x0, 0xaf, 0x90, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xfa, 0x88, 0x88, 0x88, 0x88,
    0x80, 0xd, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x10, 0x0, 0x0, 0x1, 0x0, 0x0,
    0xdf, 0xe5, 0x0, 0x4, 0xcc, 0x0, 0x0, 0x2c,
    0xff, 0xfe, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x6b,
    0xef, 0xeb, 0x50, 0x0,

    /* U+0066 "f" */
    0x0, 0x1, 0x9e, 0xfd, 0x70, 0x0, 0xdf, 0xfc,
    0xe9, 0x0, 0x5f, 0xe1, 0x0, 0x10, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0x9f, 0x90, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf2, 0x8c, 0xef, 0xec, 0xcc, 0x10,
    0x9, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x0,
    0x0, 0x9f, 0xa0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x1, 0x7c, 0xef, 0xd8, 0x15, 0xfc, 0x0,
    0x4e, 0xff, 0xfe, 0xff, 0xf9, 0xfc, 0x2, 0xff,
    0xd4, 0x0, 0x6, 0xef, 0xfc, 0x9, 0xfc, 0x0,
    0x0, 0x0, 0x1e, 0xfc, 0xe, 0xf5, 0x0, 0x0,
    0x0, 0x8, 0xfc, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x6, 0xfc, 0xe, 0xf6, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x9, 0xfd, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0x2, 0xff, 0xc3, 0x0, 0x5, 0xef, 0xfc, 0x0,
    0x4e, 0xff, 0xfe, 0xff, 0xfa, 0xfc, 0x0, 0x1,
    0x7c, 0xef, 0xd9, 0x17, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0xed, 0x62, 0x0, 0x4,
    0xdf, 0xe0, 0x1, 0xcf, 0xff, 0xfe, 0xff, 0xfe,
    0x30, 0x0, 0x4, 0x9d, 0xff, 0xec, 0x81, 0x0,

    /* U+0068 "h" */
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xef, 0x43,
    0xae, 0xfe, 0xb4, 0x0, 0xef, 0xaf, 0xff, 0xff,
    0xff, 0x60, 0xef, 0xfc, 0x30, 0x3, 0xcf, 0xf1,
    0xef, 0xd0, 0x0, 0x0, 0x1e, 0xf7, 0xef, 0x70,
    0x0, 0x0, 0xa, 0xf9, 0xef, 0x50, 0x0, 0x0,
    0x8, 0xfa, 0xef, 0x40, 0x0, 0x0, 0x8, 0xfa,
    0xef, 0x40, 0x0, 0x0, 0x8, 0xfa, 0xef, 0x40,
    0x0, 0x0, 0x8, 0xfa, 0xef, 0x40, 0x0, 0x0,
    0x8, 0xfa, 0xef, 0x40, 0x0, 0x0, 0x8, 0xfa,
    0xef, 0x40, 0x0, 0x0, 0x8, 0xfa,

    /* U+0069 "i" */
    0xc, 0xe4, 0x3f, 0xfa, 0x9, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf4, 0xe, 0xf4, 0xe, 0xf4,
    0xe, 0xf4, 0xe, 0xf4, 0xe, 0xf4, 0xe, 0xf4,
    0xe, 0xf4, 0xe, 0xf4, 0xe, 0xf4, 0xe, 0xf4,
    0xe, 0xf4,

    /* U+006A "j" */
    0x0, 0x0, 0xb, 0xf6, 0x0, 0x0, 0x2f, 0xfc,
    0x0, 0x0, 0x8, 0xb3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xd, 0xf6,
    0x2, 0x0, 0x3f, 0xf3, 0xc, 0xfd, 0xff, 0xb0,
    0x9, 0xef, 0xe9, 0x0,

    /* U+006B "k" */
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0x9, 0xfe, 0x20, 0xef,
    0x40, 0x0, 0xa, 0xfe, 0x20, 0xe, 0xf4, 0x0,
    0x1c, 0xfe, 0x20, 0x0, 0xef, 0x40, 0x1d, 0xfd,
    0x10, 0x0, 0xe, 0xf4, 0x2e, 0xfd, 0x10, 0x0,
    0x0, 0xef, 0x8e, 0xff, 0xd1, 0x0, 0x0, 0xe,
    0xff, 0xfb, 0xff, 0xb0, 0x0, 0x0, 0xef, 0xfa,
    0x4, 0xff, 0x80, 0x0, 0xe, 0xf9, 0x0, 0x7,
    0xff, 0x50, 0x0, 0xef, 0x40, 0x0, 0xa, 0xff,
    0x20, 0xe, 0xf4, 0x0, 0x0, 0xc, 0xfd, 0x10,
    0xef, 0x40, 0x0, 0x0, 0x1e, 0xfb, 0x0,

    /* U+006C "l" */
    0xef, 0x4e, 0xf4, 0xef, 0x4e, 0xf4, 0xef, 0x4e,
    0xf4, 0xef, 0x4e, 0xf4, 0xef, 0x4e, 0xf4, 0xef,
    0x4e, 0xf4, 0xef, 0x4e, 0xf4, 0xef, 0x4e, 0xf4,
    0xef, 0x40,

    /* U+006D "m" */
    0xef, 0x34, 0xbe, 0xfe, 0x92, 0x0, 0x5b, 0xef,
    0xe9, 0x10, 0xe, 0xfb, 0xff, 0xde, 0xff, 0xf3,
    0xbf, 0xfd, 0xef, 0xfe, 0x20, 0xef, 0xfa, 0x10,
    0x3, 0xdf, 0xff, 0xa1, 0x0, 0x3d, 0xfc, 0xe,
    0xfd, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0,
    0x4f, 0xf2, 0xef, 0x70, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0xff, 0x4e, 0xf5, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x0, 0x0, 0xe, 0xf5, 0xef,
    0x40, 0x0, 0x0, 0xe, 0xf5, 0x0, 0x0, 0x0,
    0xef, 0x5e, 0xf4, 0x0, 0x0, 0x0, 0xef, 0x50,
    0x0, 0x0, 0xe, 0xf5, 0xef, 0x40, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0xef, 0x5e, 0xf4,
    0x0, 0x0, 0x0, 0xef, 0x50, 0x0, 0x0, 0xe,
    0xf5, 0xef, 0x40, 0x0, 0x0, 0xe, 0xf5, 0x0,
    0x0, 0x0, 0xef, 0x5e, 0xf4, 0x0, 0x0, 0x0,
    0xef, 0x50, 0x0, 0x0, 0xe, 0xf5,

    /* U+006E "n" */
    0xef, 0x33, 0xae, 0xfe, 0xb4, 0x0, 0xef, 0xaf,
    0xfd, 0xdf, 0xff, 0x60, 0xef, 0xfb, 0x20, 0x1,
    0xaf, 0xf1, 0xef, 0xd0, 0x0, 0x0, 0xe, 0xf7,
    0xef, 0x70, 0x0, 0x0, 0xa, 0xf9, 0xef, 0x50,
    0x0, 0x0, 0x8, 0xfa, 0xef, 0x40, 0x0, 0x0,
    0x8, 0xfa, 0xef, 0x40, 0x0, 0x0, 0x8, 0xfa,
    0xef, 0x40, 0x0, 0x0, 0x8, 0xfa, 0xef, 0x40,
    0x0, 0x0, 0x8, 0xfa, 0xef, 0x40, 0x0, 0x0,
    0x8, 0xfa, 0xef, 0x40, 0x0, 0x0, 0x8, 0xfa,

    /* U+006F "o" */
    0x0, 0x0, 0x6c, 0xef, 0xda, 0x30, 0x0, 0x0,
    0x2d, 0xff, 0xfe, 0xff, 0xf8, 0x0, 0x0, 0xdf,
    0xd4, 0x0, 0x18, 0xff, 0x70, 0x7, 0xfe, 0x10,
    0x0, 0x0, 0x6f, 0xf1, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0xd, 0xf6, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0xf, 0xf4, 0x0, 0x0, 0x0, 0xa,
    0xf9, 0xd, 0xf7, 0x0, 0x0, 0x0, 0xd, 0xf6,
    0x7, 0xfe, 0x10, 0x0, 0x0, 0x6f, 0xf1, 0x0,
    0xdf, 0xd4, 0x0, 0x18, 0xff, 0x70, 0x0, 0x2c,
    0xff, 0xfe, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x6b,
    0xef, 0xda, 0x30, 0x0,

    /* U+0070 "p" */
    0xef, 0x33, 0xae, 0xfe, 0xb4, 0x0, 0xe, 0xf9,
    0xff, 0xec, 0xef, 0xfa, 0x0, 0xef, 0xfd, 0x30,
    0x0, 0x5e, 0xfa, 0xe, 0xfe, 0x10, 0x0, 0x0,
    0x4f, 0xf2, 0xef, 0x80, 0x0, 0x0, 0x0, 0xcf,
    0x8e, 0xf5, 0x0, 0x0, 0x0, 0x9, 0xfa, 0xef,
    0x50, 0x0, 0x0, 0x0, 0x9f, 0xae, 0xf9, 0x0,
    0x0, 0x0, 0xd, 0xf8, 0xef, 0xf2, 0x0, 0x0,
    0x5, 0xff, 0x2e, 0xff, 0xe5, 0x0, 0x17, 0xff,
    0xa0, 0xef, 0x9f, 0xff, 0xef, 0xff, 0xa0, 0xe,
    0xf4, 0x29, 0xef, 0xeb, 0x50, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x7c, 0xef, 0xd7, 0x8, 0xf9, 0x0,
    0x2d, 0xff, 0xfe, 0xff, 0xda, 0xf9, 0x1, 0xef,
    0xd4, 0x0, 0x18, 0xff, 0xf9, 0x8, 0xfe, 0x10,
    0x0, 0x0, 0x6f, 0xf9, 0xd, 0xf7, 0x0, 0x0,
    0x0, 0xd, 0xf9, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0xf, 0xf4, 0x0, 0x0, 0x0, 0xa,
    0xf9, 0xd, 0xf7, 0x0, 0x0, 0x0, 0xd, 0xf9,
    0x8, 0xfe, 0x10, 0x0, 0x0, 0x6f, 0xf9, 0x1,
    0xef, 0xd4, 0x0, 0x18, 0xff, 0xf9, 0x0, 0x2d,
    0xff, 0xfe, 0xff, 0xdb, 0xf9, 0x0, 0x0, 0x7c,
    0xef, 0xd7, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf9,

    /* U+0072 "r" */
    0xef, 0x33, 0xae, 0x9e, 0xf8, 0xff, 0xfa, 0xef,
    0xfe, 0x51, 0xe, 0xfe, 0x10, 0x0, 0xef, 0x80,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0xef, 0x40, 0x0,
    0xe, 0xf4, 0x0, 0x0, 0xef, 0x40, 0x0, 0xe,
    0xf4, 0x0, 0x0, 0xef, 0x40, 0x0, 0xe, 0xf4,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x28, 0xdf, 0xfd, 0xa5, 0x0, 0x4f, 0xff,
    0xee, 0xff, 0xf3, 0xd, 0xfa, 0x10, 0x0, 0x58,
    0x0, 0xff, 0x20, 0x0, 0x0, 0x0, 0xe, 0xfb,
    0x30, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfc, 0x84,
    0x0, 0x0, 0x17, 0xbe, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x1, 0x5e, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xd0, 0xc8, 0x20, 0x0, 0x2c, 0xfa, 0x3f,
    0xff, 0xfe, 0xef, 0xfe, 0x20, 0x28, 0xce, 0xff,
    0xc8, 0x10,

    /* U+0074 "t" */
    0x0, 0x48, 0x50, 0x0, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf2, 0x8c, 0xef, 0xec, 0xcc, 0x10, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0,
    0x9, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0xa0, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x6f,
    0xe2, 0x0, 0x10, 0x0, 0xef, 0xfd, 0xfa, 0x0,
    0x1, 0xae, 0xfc, 0x60,

    /* U+0075 "u" */
    0xf, 0xf3, 0x0, 0x0, 0x0, 0xcf, 0x70, 0xff,
    0x30, 0x0, 0x0, 0xc, 0xf7, 0xf, 0xf3, 0x0,
    0x0, 0x0, 0xcf, 0x70, 0xff, 0x30, 0x0, 0x0,
    0xc, 0xf7, 0xf, 0xf3, 0x0, 0x0, 0x0, 0xcf,
    0x70, 0xff, 0x30, 0x0, 0x0, 0xc, 0xf7, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0xcf, 0x70, 0xff, 0x40,
    0x0, 0x0, 0xe, 0xf7, 0xc, 0xf9, 0x0, 0x0,
    0x5, 0xff, 0x70, 0x7f, 0xf6, 0x0, 0x4, 0xef,
    0xf7, 0x0, 0xbf, 0xff, 0xde, 0xfe, 0xcf, 0x70,
    0x0, 0x6c, 0xff, 0xd8, 0x1a, 0xf7,

    /* U+0076 "v" */
    0xd, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x0, 0xef,
    0x50, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x7f, 0xc0,
    0x0, 0x0, 0xdf, 0x50, 0x0, 0x1f, 0xf3, 0x0,
    0x4, 0xfd, 0x0, 0x0, 0x9, 0xfa, 0x0, 0xb,
    0xf6, 0x0, 0x0, 0x2, 0xff, 0x10, 0x2f, 0xf0,
    0x0, 0x0, 0x0, 0xbf, 0x80, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x4f, 0xe1, 0xff, 0x10, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xc0, 0x0, 0x0,

    /* U+0077 "w" */
    0xaf, 0x60, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0xaf, 0x54, 0xfc, 0x0, 0x0, 0x2, 0xff,
    0xd0, 0x0, 0x0, 0xf, 0xe0, 0xe, 0xf2, 0x0,
    0x0, 0x8f, 0xff, 0x40, 0x0, 0x6, 0xf9, 0x0,
    0x8f, 0x80, 0x0, 0xe, 0xf8, 0xfa, 0x0, 0x0,
    0xcf, 0x30, 0x2, 0xfe, 0x0, 0x4, 0xfb, 0x1f,
    0xf0, 0x0, 0x2f, 0xd0, 0x0, 0xc, 0xf4, 0x0,
    0xaf, 0x50, 0xaf, 0x60, 0x8, 0xf7, 0x0, 0x0,
    0x6f, 0xa0, 0x1f, 0xe0, 0x4, 0xfc, 0x0, 0xef,
    0x10, 0x0, 0x1, 0xff, 0x7, 0xf8, 0x0, 0xd,
    0xf2, 0x4f, 0xb0, 0x0, 0x0, 0xa, 0xf5, 0xdf,
    0x20, 0x0, 0x7f, 0x8a, 0xf4, 0x0, 0x0, 0x0,
    0x4f, 0xef, 0xc0, 0x0, 0x1, 0xfe, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0xb,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0,
    0x0, 0x0, 0x5f, 0xf2, 0x0, 0x0,

    /* U+0078 "x" */
    0x2e, 0xf6, 0x0, 0x0, 0xa, 0xfb, 0x0, 0x4f,
    0xf3, 0x0, 0x7, 0xfd, 0x10, 0x0, 0x8f, 0xe1,
    0x3, 0xff, 0x30, 0x0, 0x0, 0xbf, 0xb1, 0xef,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0xef, 0x90, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x3f,
    0xfb, 0xfc, 0x0, 0x0, 0x0, 0x1d, 0xf7, 0xc,
    0xf9, 0x0, 0x0, 0xb, 0xfb, 0x0, 0x2e, 0xf6,
    0x0, 0x8, 0xfd, 0x10, 0x0, 0x4f, 0xf3, 0x4,
    0xff, 0x30, 0x0, 0x0, 0x8f, 0xe1,

    /* U+0079 "y" */
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xfa, 0x5,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xf2, 0x0, 0xdf,
    0x60, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0xe, 0xf5, 0x0,
    0x5, 0xfc, 0x0, 0x0, 0x7, 0xfc, 0x0, 0xc,
    0xf5, 0x0, 0x0, 0x1, 0xff, 0x30, 0x3f, 0xd0,
    0x0, 0x0, 0x0, 0x8f, 0xb0, 0xaf, 0x60, 0x0,
    0x0, 0x0, 0x1f, 0xf5, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x10, 0x0, 0x0, 0x7, 0x10, 0x1c, 0xf9, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x8, 0xdf, 0xe9, 0x10, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb, 0xcc,
    0xcc, 0xcc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x1d, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xe, 0xff, 0xcc, 0xcc,
    0xcc, 0xc1, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+007B "{" */
    0x0, 0x6, 0xdf, 0xa0, 0x5, 0xff, 0xe8, 0x0,
    0xbf, 0xa0, 0x0, 0xd, 0xf6, 0x0, 0x0, 0xdf,
    0x50, 0x0, 0xd, 0xf5, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0xd, 0xf5, 0x0, 0x0, 0xdf, 0x50, 0x0,
    0x1f, 0xf4, 0x0, 0xbf, 0xfa, 0x0, 0x8, 0xdf,
    0xd1, 0x0, 0x0, 0xef, 0x50, 0x0, 0xd, 0xf5,
    0x0, 0x0, 0xdf, 0x50, 0x0, 0xd, 0xf5, 0x0,
    0x0, 0xdf, 0x50, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x6, 0xff, 0xd7, 0x0, 0x7,
    0xdf, 0xa0,

    /* U+007C "|" */
    0x9f, 0x79, 0xf7, 0x9f, 0x79, 0xf7, 0x9f, 0x79,
    0xf7, 0x9f, 0x79, 0xf7, 0x9f, 0x79, 0xf7, 0x9f,
    0x79, 0xf7, 0x9f, 0x79, 0xf7, 0x9f, 0x79, 0xf7,
    0x9f, 0x79, 0xf7, 0x9f, 0x79, 0xf7, 0x9f, 0x70,

    /* U+007D "}" */
    0x9f, 0xd7, 0x0, 0x7, 0xdf, 0xf6, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x4f, 0xf0,
    0x0, 0x4, 0xff, 0x0, 0x0, 0x4f, 0xf0, 0x0,
    0x2, 0xff, 0x20, 0x0, 0x9, 0xff, 0xb0, 0x0,
    0xdf, 0xe9, 0x0, 0x3f, 0xf0, 0x0, 0x4, 0xff,
    0x0, 0x0, 0x4f, 0xf0, 0x0, 0x4, 0xff, 0x0,
    0x0, 0x4f, 0xf0, 0x0, 0x4, 0xfe, 0x0, 0x0,
    0x8f, 0xd0, 0x6, 0xdf, 0xf8, 0x0, 0x9f, 0xd8,
    0x0, 0x0,

    /* U+007E "~" */
    0x1, 0x7a, 0x70, 0x0, 0x4, 0xa0, 0xd, 0xff,
    0xfd, 0x30, 0xa, 0xd0, 0x6f, 0x60, 0x6e, 0xfa,
    0xaf, 0x70, 0x9d, 0x0, 0x1, 0x9f, 0xe9, 0x0,

    /* U+00A0 " " */

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0xae, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xa5, 0xdf, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xc8,
    0x30, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfb, 0x61, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x49, 0xba, 0xef, 0xf1,
    0x0, 0x0, 0x1, 0xff, 0xc0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0xc0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xf1,
    0x1, 0x8c, 0xdc, 0xff, 0xc0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0x80,
    0xbf, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x1,
    0x8d, 0xfe, 0xb4, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8c, 0xdc, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x11, 0x0, 0x34, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x20, 0x1, 0x1e, 0x70, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x7e, 0xff,
    0xdd, 0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xff,
    0xfd, 0xdf, 0xff, 0xda, 0xaf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfe, 0xaa, 0xdf, 0xf7, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x90,
    0x7, 0xff, 0x70, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf9, 0x0, 0x7f, 0xfc, 0x88, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe8, 0x8c,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xcf, 0xeb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xef, 0xa0, 0x7, 0xff,
    0x70, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x7f, 0xf9, 0x33, 0xdf, 0xb7, 0x77,
    0x77, 0x77, 0x77, 0xdf, 0xc3, 0x39, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xfa, 0x44, 0xef, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc4, 0x4a, 0xff, 0x70, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf9, 0x0,
    0x7f, 0xf7, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xa0, 0x7, 0xff, 0xfe, 0xef, 0xf8,
    0x22, 0x22, 0x22, 0x22, 0x2b, 0xff, 0xee, 0xff,
    0xfd, 0x99, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe9, 0x9d, 0xfb, 0x70, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x7b,

    /* U+F00B "" */
    0x7b, 0xbb, 0xbb, 0x30, 0x5b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x7f, 0xff, 0xff, 0xfa, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xf7, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xbb, 0xbb, 0xb3, 0x5, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7, 0xff, 0xff, 0xff, 0xa0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0x70, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x11, 0x11, 0x0, 0x1, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0xc, 0xff, 0xff, 0xf7,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6a, 0xaa,
    0xaa, 0x30, 0x5a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0x60,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0x90, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0x90,
    0x0, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0x80, 0x0, 0x9f, 0xff, 0xf3, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0x80, 0x0, 0xf,
    0xff, 0xff, 0xf3, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf3, 0x1,
    0xdf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xf5, 0xdf, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x4, 0x93, 0x0, 0x0, 0x0, 0x0, 0x49, 0x30,
    0x5f, 0xff, 0x30, 0x0, 0x0, 0x5, 0xff, 0xf3,
    0xef, 0xff, 0xf3, 0x0, 0x0, 0x6f, 0xff, 0xfb,
    0xbf, 0xff, 0xff, 0x30, 0x6, 0xff, 0xff, 0xf8,
    0xc, 0xff, 0xff, 0xf3, 0x6f, 0xff, 0xff, 0xa0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x6, 0xff, 0xff, 0xfa, 0xcf, 0xff, 0xff, 0x30,
    0x5f, 0xff, 0xff, 0xa0, 0xc, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0xcf, 0xff, 0xfb,
    0xbf, 0xff, 0xa0, 0x0, 0x0, 0xc, 0xff, 0xf8,
    0xc, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0x60,
    0x1, 0xff, 0xf8, 0x0, 0x1c, 0x90, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf1, 0x1, 0xff, 0xf8, 0x0,
    0xaf, 0xfb, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf6,
    0x1, 0xff, 0xf8, 0x0, 0xef, 0xff, 0xa0, 0x0,
    0x0, 0xef, 0xff, 0xc0, 0x1, 0xff, 0xf8, 0x0,
    0x5f, 0xff, 0xf5, 0x0, 0x7, 0xff, 0xfc, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0x5, 0xff, 0xfe, 0x0,
    0xe, 0xff, 0xf1, 0x0, 0x1, 0xff, 0xf8, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0x3f, 0xff, 0x90, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x7f, 0xff, 0x30, 0x0, 0x1, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xff, 0xe0, 0x9f, 0xff, 0x0, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x9f, 0xff, 0x0, 0x0, 0x1, 0xff, 0xf8, 0x0,
    0x0, 0x8, 0xff, 0xf0, 0x9f, 0xff, 0x0, 0x0,
    0x0, 0x9d, 0xc3, 0x0, 0x0, 0xa, 0xff, 0xf0,
    0x6f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xd0, 0x2f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x90,
    0xd, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x40, 0x5, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfc, 0x0,
    0x0, 0xcf, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xf3, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xc6, 0x21, 0x24, 0x9f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xfd,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x14, 0x77, 0x75, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9b, 0xcb, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x3, 0xcf, 0xff, 0xff, 0xc3, 0x0, 0x13,
    0x0, 0x0, 0x3, 0xfe, 0x69, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x6e, 0xf3, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xec, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0x60, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x7f, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x70, 0x0, 0x0,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x1f, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0x10, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf0,
    0x0, 0x2, 0xbf, 0xff, 0xff, 0x90, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xb2, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xa2, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xf1,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x1, 0xec, 0x25, 0xef, 0xff,
    0xff, 0xff, 0xe5, 0x2b, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x87, 0x50,
    0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdc, 0x30,
    0x0, 0x8e, 0xec, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xf6, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x90, 0xaf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xff, 0x9a, 0xff, 0xfb,
    0xbf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xff, 0xf5, 0x0, 0x7f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xfe, 0x30, 0x98,
    0x4, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xc1, 0x1c, 0xff, 0xb0, 0x2d, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xfa, 0x3,
    0xef, 0xff, 0xfd, 0x21, 0xbf, 0xff, 0xa0, 0x0,
    0x2, 0xef, 0xff, 0x70, 0x5f, 0xff, 0xff, 0xff,
    0xe3, 0x9, 0xff, 0xfc, 0x10, 0x5f, 0xff, 0xf4,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x6f,
    0xff, 0xe3, 0xdf, 0xfd, 0x20, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x4, 0xef, 0xfb, 0x3f,
    0xc1, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x2d, 0xf2, 0x2, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf8, 0x77, 0x8f, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xc0, 0x0, 0xe, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xc0, 0x0, 0xe,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xc0, 0x0, 0xe, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xc0,
    0x0, 0xe, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xb0, 0x0, 0xd, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3, 0x33,
    0x33, 0x10, 0x0, 0x1, 0x33, 0x33, 0x30, 0x0,
    0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x45, 0x55,
    0x9f, 0xff, 0xff, 0x95, 0x55, 0x40, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x22, 0x22, 0x22,
    0x20, 0x9f, 0xff, 0x90, 0x22, 0x22, 0x22, 0x20,
    0xdf, 0xff, 0xff, 0xff, 0x60, 0x9f, 0x90, 0x5f,
    0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x20, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa6, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xe, 0x70,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe6, 0xfb, 0x5e, 0xff, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xef, 0xfd, 0x0, 0x0, 0x0, 0xa, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x80,
    0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x1, 0xef,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfd, 0x0, 0xa, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x80,
    0x5f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf3, 0xdf, 0xfd, 0xaa,
    0xaa, 0x91, 0x0, 0x0, 0x0, 0x2a, 0xaa, 0xaa,
    0xef, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xdc, 0xcc, 0xcd, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0x10, 0x0, 0x0, 0x0, 0x16,
    0xac, 0xdc, 0x95, 0x0, 0x0, 0x2f, 0xff, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xfe, 0x80,
    0x2, 0xff, 0xf0, 0x0, 0x5, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x1f, 0xff, 0x0, 0x6,
    0xff, 0xff, 0xfa, 0x76, 0x7b, 0xff, 0xff, 0xf7,
    0xff, 0xf0, 0x4, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x2, 0xaf, 0xff, 0xff, 0xff, 0x1, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x22,
    0x21, 0x6f, 0xff, 0xff, 0xe, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x22,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf2,
    0xff, 0xff, 0xfd, 0xef, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfd, 0xf, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x50, 0xff,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xb0, 0xf, 0xff, 0xef, 0xff, 0xf9, 0x30,
    0x0, 0x28, 0xff, 0xff, 0xe1, 0x0, 0xff, 0xf2,
    0xcf, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0xf, 0xff, 0x10, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xff, 0xf2, 0x0,
    0x17, 0xdf, 0xff, 0xff, 0xfa, 0x30, 0x0, 0x0,
    0xd, 0xee, 0x10, 0x0, 0x0, 0x24, 0x54, 0x30,
    0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xf8, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x6a, 0xaa,
    0xab, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x10, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x4, 0xfc, 0x10, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x3, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x3f,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xf, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x5f, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x4, 0xff, 0x90, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x3, 0xea, 0x0, 0x6a, 0xaa, 0xab,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf8, 0x0, 0x0, 0xa9, 0x0, 0x1d, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xd1, 0x3, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xf8, 0x0, 0x0, 0x4e, 0xfb,
    0x0, 0x8f, 0xd0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x20, 0x3, 0xff, 0x60, 0x1f, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4, 0xfc,
    0x10, 0x8f, 0xd0, 0xb, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x2, 0xef, 0xb0, 0x1f, 0xf2,
    0x7, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x3f, 0xf1, 0xd, 0xf5, 0x5, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xf3,
    0xc, 0xf5, 0x4, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x6f, 0xf1, 0xe, 0xf4, 0x6,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4,
    0xff, 0x80, 0x2f, 0xf1, 0x8, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x2, 0xe9, 0x0, 0xaf,
    0xc0, 0xc, 0xf7, 0x6a, 0xaa, 0xab, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x6, 0xff, 0x40, 0x2f, 0xf2,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xf8, 0x0, 0x0,
    0x8f, 0xf9, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf8, 0x0, 0x1, 0xff, 0x90, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7,
    0x0, 0x0, 0x55, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xe4, 0x0, 0x0, 0x0,
    0x4, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xe9, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1,
    0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xe3, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x3, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xcc, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf5, 0x6, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xf5, 0x0, 0x6, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xef, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xf5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xe0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xdf, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x8f, 0xf9, 0x9, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x2f, 0xff, 0x60, 0x48, 0xdf, 0xff, 0xff, 0xe0,
    0x8, 0xff, 0xfa, 0x30, 0x8f, 0xff, 0xff, 0x50,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xdb, 0x61, 0x0, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xee, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xa0,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0x4, 0xff, 0xf3,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0x5f, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x0, 0x7, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x0, 0x8f, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x9, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0xaf, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x12, 0xef, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x1d, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x1, 0xcf, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x0, 0xb, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0xaf, 0xff, 0xf4,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0x9, 0xff, 0xf3,
    0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xe1,
    0x3, 0x33, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,

    /* U+F04B "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc2, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xbd, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x3c, 0xff, 0xff, 0xea, 0x0, 0x0, 0x9e, 0xff,
    0xff, 0xd3, 0xd, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x8, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff,
    0xff, 0x80, 0x7, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x22, 0x22, 0x20, 0x0, 0x0,
    0x2, 0x22, 0x22, 0x10, 0x0,

    /* U+F04D "" */
    0x2c, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xc4, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x1, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x10, 0x0,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xd5, 0x0, 0x0, 0x0, 0x0, 0xde, 0xe2,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0x90, 0x0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xfc, 0x10, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0x30, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xff, 0xe2, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xfd, 0x10, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xc1, 0x0, 0x0, 0xff, 0xf4,
    0x2f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xff, 0xf4,
    0xd, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf3,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x23, 0x30,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xcc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x4, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x50,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0x10, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xee, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F054 "" */
    0x2, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x60, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x60, 0x0, 0x0, 0xa, 0xff, 0xff, 0x60,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x60, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x7d, 0xd9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x15, 0x55,
    0x55, 0x56, 0xff, 0xff, 0x75, 0x55, 0x55, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x15, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x51, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x33, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8c, 0xff, 0xff, 0xff, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfd, 0x61, 0x0, 0x17, 0xef,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x9f, 0xff, 0xfc, 0x0, 0x0, 0xde,
    0x91, 0x0, 0xdf, 0xff, 0xf7, 0x0, 0x6, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0xdf, 0xfe, 0x20, 0x4f,
    0xff, 0xff, 0x40, 0x2f, 0xff, 0xff, 0xc0, 0x0,
    0x1, 0xff, 0xff, 0xc0, 0xe, 0xff, 0xff, 0xe1,
    0xbf, 0xff, 0xff, 0x80, 0x37, 0x6d, 0xff, 0xff,
    0xf2, 0xa, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff,
    0x70, 0x5f, 0xff, 0xff, 0xff, 0xf3, 0x9, 0xff,
    0xff, 0xfd, 0x9f, 0xff, 0xff, 0x90, 0x3f, 0xff,
    0xff, 0xff, 0xf1, 0xb, 0xff, 0xff, 0xf7, 0xe,
    0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff, 0xa0,
    0xf, 0xff, 0xff, 0xd0, 0x4, 0xff, 0xff, 0xf4,
    0x1, 0xdf, 0xff, 0xfc, 0x0, 0x6f, 0xff, 0xff,
    0x20, 0x0, 0x5f, 0xff, 0xfe, 0x10, 0x6, 0xaa,
    0x50, 0x2, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xa5, 0x33, 0x5a, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xcd, 0xff, 0xec, 0x83, 0x0, 0x0,
    0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x23, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0x90, 0x4, 0x9d,
    0xff, 0xff, 0xfe, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xcd, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xfb, 0x40, 0x0, 0x3a,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c,
    0xff, 0xfb, 0x16, 0xec, 0x60, 0x6, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x80, 0x0, 0x9, 0xff,
    0xfd, 0x7f, 0xff, 0x90, 0xc, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x9f, 0xd2, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x5f, 0xff, 0xff, 0x80, 0x0,
    0x3f, 0xff, 0xe4, 0x0, 0x2, 0xdf, 0xff, 0xff,
    0xfa, 0x2, 0xff, 0xff, 0xff, 0x20, 0x6, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xb0,
    0x1f, 0xff, 0xff, 0xf5, 0x0, 0x1f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x3, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x7f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x3e, 0xff, 0xf8, 0x8f, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xbf, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xfe, 0x74, 0x23, 0x0, 0x2, 0xdf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0xaf, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6a,
    0xdf, 0xfe, 0xd6, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xb0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xed, 0xde,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x0, 0x2, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x0, 0x2, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0x10, 0x3, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0x20, 0x4,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x30, 0x5, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0x40, 0x6, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0x60, 0x7, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x3, 0xce, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xec, 0x20,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5b, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x20, 0xef, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff,
    0xfe, 0x2f, 0xff, 0xff, 0xff, 0x30, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xb9, 0xaa, 0xad, 0xff, 0xfd, 0x0, 0xcf, 0xff,
    0xda, 0xef, 0xff, 0xc0, 0x0, 0x0, 0x1d, 0xff,
    0x40, 0xbf, 0xff, 0xe2, 0xc, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x1e, 0x50, 0xaf, 0xff, 0xe2, 0x0,
    0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf3, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x5, 0x0, 0x6, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf6, 0x9, 0xf7, 0x0, 0xbf, 0xf3,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf8, 0x7, 0xff,
    0xf6, 0xc, 0xff, 0xf3, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfa, 0x79,
    0x99, 0x98, 0x0, 0x0, 0x0, 0x0, 0x79, 0x9e,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x15, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x8f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xa0, 0x8, 0xff, 0xff, 0x90, 0x0, 0x0, 0x7f,
    0xff, 0xf9, 0x0, 0x0, 0x8f, 0xff, 0xf9, 0x0,
    0x7, 0xff, 0xff, 0x90, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x90, 0x6f, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf8, 0xaf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfc, 0x1d, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe2,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x10,

    /* U+F078 "" */
    0x3, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x40, 0x3f, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf4, 0xaf, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x3f, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5,
    0x3, 0xff, 0xff, 0xc1, 0x0, 0x0, 0xb, 0xff,
    0xff, 0x50, 0x0, 0x3f, 0xff, 0xfc, 0x10, 0x0,
    0xbf, 0xff, 0xf5, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xc1, 0xb, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xfc, 0xbf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfc, 0x0, 0x8, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xda, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xfc,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xfc, 0x13,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x9, 0xff, 0xfe, 0xff, 0xef, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0xef,
    0xf5, 0xcf, 0xf3, 0xef, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x10, 0x0, 0x3, 0xc5, 0xc,
    0xff, 0x12, 0xb6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x10, 0x0, 0x0,
    0x0, 0xc, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x60, 0xcf, 0xf1, 0x2c, 0x70, 0x0, 0x0,
    0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x5c, 0xff, 0x3e, 0xff, 0x30, 0x0, 0xc, 0xff,
    0x21, 0x11, 0x11, 0x11, 0x10, 0x8f, 0xff, 0xef,
    0xfe, 0xff, 0xc0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x5c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xb1, 0x0, 0x7f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x1, 0x34, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe6, 0x66, 0x66, 0x66, 0x65, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x7a, 0xaa,
    0xbf, 0xff, 0xff, 0xda, 0xaa, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x22, 0x22, 0x22,
    0x2, 0xff, 0xff, 0xf9, 0x2, 0x22, 0x22, 0x20,
    0xdf, 0xff, 0xff, 0xf4, 0x1f, 0xff, 0xff, 0x74,
    0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xa0,
    0x13, 0x33, 0x30, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb6, 0x55, 0x56, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xe, 0x70,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe6, 0xfb, 0x5e, 0xff, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x96, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x18, 0xee,
    0x20, 0x0, 0x2, 0xef, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xff, 0xd1, 0x0, 0x5f, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xfc, 0x3c, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xfe, 0x92, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8d, 0xdb, 0x97,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x5a, 0xb8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x8e, 0xfc, 0x40, 0x7f, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xd,
    0xff, 0x73, 0xcf, 0xf7, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0x50, 0xff, 0xe0, 0x4, 0xff, 0x90, 0x0,
    0xbf, 0xff, 0xff, 0x50, 0xe, 0xff, 0x40, 0xaf,
    0xf8, 0x0, 0xbf, 0xff, 0xff, 0x50, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xa0, 0xbf, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x8d, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0xaf, 0xfe, 0xcf, 0xff, 0x80,
    0x7f, 0xff, 0xff, 0xc0, 0x0, 0xf, 0xff, 0x10,
    0x7f, 0xf9, 0x0, 0x6f, 0xff, 0xff, 0xc0, 0x0,
    0xff, 0xf1, 0x6, 0xff, 0x90, 0x0, 0x6f, 0xff,
    0xff, 0xc0, 0xb, 0xff, 0xeb, 0xff, 0xf5, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfc, 0x0,
    0x3d, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x16,
    0x74, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x22, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xf8, 0x3e, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0x83, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf8, 0x3f,
    0xff, 0x30, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0x83, 0xff, 0xfe, 0x8, 0xcd, 0xd4, 0x1f,
    0xff, 0xff, 0xff, 0xf8, 0x3, 0x33, 0x30, 0xff,
    0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x22,
    0x22, 0xf, 0xff, 0xf5, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x51, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff,
    0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0xf5, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0x51, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0x51, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xf5,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xf5, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x51,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xf9, 0x9, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0x90, 0xff, 0xff, 0xf7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x9, 0xde, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xd3, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc5,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0xf,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xf6, 0x0, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf5, 0xf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf0, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x1f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf1, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xee, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x4, 0xef, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0x70, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x5, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x2, 0xdf, 0xff,
    0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x1, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x10, 0x0,

    /* U+F0C9 "" */
    0x24, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x43, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x20, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x13, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x31, 0x0,

    /* U+F0E0 "" */
    0x4, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x74, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x40, 0x91, 0x1b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x6f, 0xd3, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x1, 0xbf, 0xff, 0xf6, 0x3, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x4, 0xef, 0xff,
    0xff, 0xfa, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xa1,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x6f,
    0xff, 0xff, 0x60, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x2b, 0xfb, 0x20, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0x57, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F0E7 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xc1,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xee, 0xee, 0xef, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe3, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6a, 0xbb, 0xbf,
    0xec, 0xfd, 0xbb, 0xb9, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf3, 0xe, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x63, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xcc, 0xcc, 0xcc, 0x30, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe, 0x9,
    0xff, 0xff, 0xff, 0x42, 0xc1, 0x0, 0xf, 0xff,
    0xff, 0xc0, 0xef, 0xff, 0xff, 0xf4, 0x3f, 0xd1,
    0x0, 0xff, 0xff, 0xfc, 0xe, 0xff, 0xff, 0xff,
    0x43, 0xff, 0xd1, 0xf, 0xff, 0xff, 0xc0, 0xef,
    0xff, 0xff, 0xf4, 0x3f, 0xff, 0xd0, 0xff, 0xff,
    0xfc, 0xe, 0xff, 0xff, 0xff, 0x41, 0x55, 0x55,
    0xf, 0xff, 0xff, 0xc0, 0xef, 0xff, 0xff, 0xf9,
    0x11, 0x11, 0x10, 0xff, 0xff, 0xfc, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff,
    0xc0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xfc, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0xc0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xfc,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x26,
    0xaa, 0xaa, 0x70, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xde,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5b, 0xff, 0xc5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcc, 0x60,
    0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xfd, 0xde, 0xfe, 0xdd, 0xff, 0xdd, 0xff, 0xdd,
    0xef, 0xed, 0xef, 0xfd, 0xff, 0xe0, 0x4, 0xf3,
    0x0, 0xf8, 0x0, 0x8f, 0x0, 0x5f, 0x20, 0xf,
    0xfe, 0xff, 0xe0, 0x4, 0xf3, 0x0, 0xf8, 0x0,
    0x8e, 0x0, 0x5f, 0x20, 0xf, 0xfe, 0xff, 0xf8,
    0x7a, 0xfa, 0x78, 0xfd, 0x77, 0xdf, 0x87, 0xbf,
    0x97, 0x8f, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xf1, 0x2, 0xf6, 0x0, 0xcc, 0x0,
    0x7f, 0x0, 0x3f, 0xff, 0xfe, 0xff, 0xff, 0xf0,
    0x1, 0xf5, 0x0, 0xcb, 0x0, 0x6f, 0x0, 0x2f,
    0xff, 0xfe, 0xff, 0xff, 0xf4, 0x35, 0xf8, 0x33,
    0xdd, 0x33, 0x9f, 0x43, 0x6f, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xf4, 0x48, 0xf7,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x8f, 0x64, 0x5f,
    0xfe, 0xff, 0xe0, 0x4, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x20, 0xf, 0xfe, 0xff, 0xe0,
    0x4, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x20, 0xf, 0xfe, 0xff, 0xfe, 0xef, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xff, 0xee, 0xef, 0xfd,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xcf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0xef, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x2a, 0xcd, 0xdd, 0xdd, 0xdd,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0xd6, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x1a, 0x90,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1a,
    0xf9, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1a, 0xff, 0x90, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x1a, 0xff, 0xf9, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1a, 0xff, 0xff, 0x90, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xbb, 0xbb, 0xb0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x9d,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xa0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x79, 0xab, 0xa9, 0x74,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc7,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xfe, 0x95, 0x20, 0x0,
    0x0, 0x26, 0xaf, 0xff, 0xff, 0xfc, 0x10, 0x6f,
    0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xff, 0xfe, 0x3e, 0xff, 0xfe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xfa, 0x3f, 0xfb, 0x10, 0x0,
    0x0, 0x3, 0x56, 0x76, 0x51, 0x0, 0x0, 0x0,
    0x3e, 0xfd, 0x10, 0x37, 0x0, 0x0, 0x2, 0x9e,
    0xff, 0xff, 0xff, 0xfd, 0x81, 0x0, 0x0, 0x18,
    0x10, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xd8, 0x42, 0x13, 0x59, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x68,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x40, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0xff, 0xe0, 0x7b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb0, 0x8f, 0xff,
    0xcf, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7, 0xef, 0xfc, 0xff,
    0xe0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1, 0xff, 0xcf, 0xfe, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1, 0xff, 0xcf, 0xfe, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7, 0xff,
    0xfc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xcf,
    0xfe, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x18, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1a, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x40, 0x0,

    /* U+F241 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x40, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0xff, 0xe0, 0xab, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x50, 0x0, 0x0, 0x8f, 0xff,
    0xcf, 0xfe, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x7, 0xef, 0xfc, 0xff,
    0xe0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x1, 0xff, 0xcf, 0xfe, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x1, 0xff, 0xcf, 0xfe, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xcf,
    0xfe, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x18, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1a, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x40, 0x0,

    /* U+F242 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x40, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0xff, 0xe0, 0xab, 0xbb, 0xbb, 0xbb,
    0xbb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xcf, 0xfe, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0xff,
    0xe0, 0xef, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xcf, 0xfe, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xcf, 0xfe, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xcf,
    0xfe, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x18, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1a, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x40, 0x0,

    /* U+F243 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x40, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0xff, 0xe0, 0x7b, 0xbb, 0xbb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xcf, 0xfe, 0xb, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0xff,
    0xe0, 0xbf, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xcf, 0xfe, 0xb,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0xbf, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xcf, 0xfe, 0xb, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xcf,
    0xfe, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x18, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1a, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x40, 0x0,

    /* U+F244 "" */
    0x2a, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdc, 0x40, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xfc, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xcf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xcf,
    0xfe, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x18, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1a, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb,
    0x40, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x97, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x9b, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0xb, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xde, 0xa1, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x70, 0x0, 0x6, 0xff, 0xff,
    0xc0, 0x4, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xe5, 0x0, 0xdf, 0xff, 0xff, 0xa9,
    0xef, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0xff, 0xfb, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xaf, 0xff, 0xff, 0x20, 0x0, 0x7, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd4, 0x1,
    0xdf, 0xff, 0x60, 0x0, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x70, 0x0, 0x0, 0x45,
    0x10, 0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xce, 0x0, 0x4c, 0xcc, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x27, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x6a, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xdf, 0xff, 0xfd, 0x81,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xef, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x5b,
    0xff, 0xff, 0xf5, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x50, 0xcf, 0xff, 0xfe, 0x10, 0xa, 0xff, 0xff,
    0xff, 0x50, 0x1d, 0xff, 0xff, 0x70, 0xf, 0xff,
    0xff, 0xff, 0x50, 0x1, 0xef, 0xff, 0xc0, 0x4f,
    0xff, 0x9a, 0xff, 0x50, 0xc1, 0x2e, 0xff, 0xf0,
    0x7f, 0xff, 0x10, 0xaf, 0x50, 0xfa, 0x4, 0xff,
    0xf3, 0x9f, 0xff, 0xd1, 0xa, 0x50, 0xd2, 0x1d,
    0xff, 0xf5, 0xaf, 0xff, 0xfd, 0x10, 0x10, 0x10,
    0xcf, 0xff, 0xf6, 0xbf, 0xff, 0xff, 0xd1, 0x0,
    0xb, 0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff, 0xfb,
    0x0, 0x6f, 0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff,
    0xd1, 0x0, 0xb, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xfd, 0x10, 0x10, 0x10, 0xbf, 0xff, 0xf6, 0xaf,
    0xff, 0xd1, 0x9, 0x50, 0xd1, 0xc, 0xff, 0xf5,
    0x7f, 0xff, 0x10, 0x9f, 0x50, 0xfa, 0x2, 0xff,
    0xf3, 0x4f, 0xff, 0x99, 0xff, 0x50, 0xd1, 0x1d,
    0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0x50, 0x11,
    0xdf, 0xff, 0xc0, 0xa, 0xff, 0xff, 0xff, 0x50,
    0x1c, 0xff, 0xff, 0x60, 0x2, 0xff, 0xff, 0xff,
    0x51, 0xcf, 0xff, 0xfe, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x7c, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x3,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x5, 0x9c, 0xdd, 0xca, 0x61, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x3, 0x44, 0x44, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xad, 0xdd, 0xdd,
    0xff, 0xff, 0xff, 0xff, 0xdd, 0xdd, 0xdc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x10, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x9f,
    0xff, 0xdf, 0xff, 0xee, 0xff, 0xfd, 0xff, 0xfb,
    0x0, 0x9, 0xff, 0xf1, 0x9f, 0xf6, 0x4f, 0xfb,
    0xe, 0xff, 0xb0, 0x0, 0x9f, 0xff, 0x8, 0xff,
    0x53, 0xff, 0xa0, 0xef, 0xfb, 0x0, 0x9, 0xff,
    0xf0, 0x8f, 0xf5, 0x3f, 0xfa, 0xe, 0xff, 0xb0,
    0x0, 0x9f, 0xff, 0x8, 0xff, 0x53, 0xff, 0xa0,
    0xef, 0xfb, 0x0, 0x9, 0xff, 0xf0, 0x8f, 0xf5,
    0x3f, 0xfa, 0xe, 0xff, 0xb0, 0x0, 0x9f, 0xff,
    0x8, 0xff, 0x53, 0xff, 0xa0, 0xef, 0xfb, 0x0,
    0x9, 0xff, 0xf0, 0x8f, 0xf5, 0x3f, 0xfa, 0xe,
    0xff, 0xb0, 0x0, 0x9f, 0xff, 0x8, 0xff, 0x53,
    0xff, 0xa0, 0xef, 0xfb, 0x0, 0x9, 0xff, 0xf0,
    0x8f, 0xf5, 0x3f, 0xfa, 0xe, 0xff, 0xb0, 0x0,
    0x9f, 0xff, 0x8, 0xff, 0x53, 0xff, 0xa0, 0xef,
    0xfb, 0x0, 0x9, 0xff, 0xf3, 0xaf, 0xf7, 0x5f,
    0xfc, 0x1f, 0xff, 0xb0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x8, 0xde, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xed, 0x90, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x80, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf8,
    0x8, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x80, 0x8f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xf8, 0x8, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x80, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xdb, 0x98, 0x62,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x13, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x43, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xf8, 0xaf, 0xff, 0xfe, 0x6e, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x9f, 0xfe, 0x20, 0x2e, 0xff, 0xff, 0xfc, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x9e,
    0x20, 0x0, 0xdf, 0xff, 0xff, 0xc0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x10, 0x0,
    0xcf, 0xff, 0xff, 0xfc, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xfc, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xc0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x1,
    0x40, 0x0, 0x9f, 0xff, 0xff, 0xfc, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xcf, 0x50,
    0x0, 0xcf, 0xff, 0xff, 0xc0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xcf, 0xff, 0x50, 0x4f,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfc, 0xdf, 0xff, 0xff, 0xaf, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0xc, 0xfd, 0xdf, 0xed,
    0xdf, 0xdd, 0xff, 0xf0, 0x0, 0xcf, 0xf0, 0xe,
    0x80, 0x3f, 0x30, 0xcf, 0xf1, 0x1c, 0xff, 0xf0,
    0xe, 0x80, 0x3f, 0x30, 0xcf, 0xf1, 0xcf, 0xff,
    0xf0, 0xe, 0x80, 0x3f, 0x30, 0xcf, 0xf1, 0xff,
    0xff, 0xf0, 0xe, 0x80, 0x3f, 0x30, 0xcf, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x7,
    0xcd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xc7, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xf1, 0x0, 0x0, 0xa, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf1,
    0x0, 0x0, 0xbf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf1, 0x0, 0x1c, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf1,
    0x1, 0xdf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf1, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x9, 0xff, 0xff, 0xfb, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x60, 0x0, 0x8f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 99, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 99, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 32, .adv_w = 144, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 57, .adv_w = 259, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 185, .adv_w = 229, .box_w = 14, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 339, .adv_w = 310, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 491, .adv_w = 252, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 619, .adv_w = 77, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 630, .adv_w = 124, .box_w = 6, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 693, .adv_w = 124, .box_w = 6, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 756, .adv_w = 147, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 797, .adv_w = 214, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 852, .adv_w = 84, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 866, .adv_w = 141, .box_w = 7, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 873, .adv_w = 84, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 881, .adv_w = 130, .box_w = 10, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 986, .adv_w = 245, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1098, .adv_w = 136, .box_w = 7, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1154, .adv_w = 211, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1258, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1362, .adv_w = 246, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1490, .adv_w = 211, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1594, .adv_w = 227, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1698, .adv_w = 220, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1802, .adv_w = 237, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1906, .adv_w = 227, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2018, .adv_w = 84, .box_w = 4, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2042, .adv_w = 84, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2074, .adv_w = 214, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2135, .adv_w = 214, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 2174, .adv_w = 214, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2235, .adv_w = 211, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2331, .adv_w = 381, .box_w = 22, .box_h = 20, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2551, .adv_w = 269, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2695, .adv_w = 279, .box_w = 15, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2815, .adv_w = 266, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2935, .adv_w = 304, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3063, .adv_w = 247, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3167, .adv_w = 234, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3263, .adv_w = 284, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3383, .adv_w = 299, .box_w = 15, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3503, .adv_w = 114, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3527, .adv_w = 189, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3615, .adv_w = 265, .box_w = 15, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3735, .adv_w = 219, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3831, .adv_w = 351, .box_w = 18, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3975, .adv_w = 299, .box_w = 15, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4095, .adv_w = 309, .box_w = 18, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4239, .adv_w = 266, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4351, .adv_w = 309, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4522, .adv_w = 268, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4634, .adv_w = 229, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4746, .adv_w = 216, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4858, .adv_w = 291, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4970, .adv_w = 262, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5114, .adv_w = 414, .box_w = 26, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5322, .adv_w = 248, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5450, .adv_w = 238, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5578, .adv_w = 242, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5698, .adv_w = 123, .box_w = 6, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 5761, .adv_w = 130, .box_w = 10, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5866, .adv_w = 123, .box_w = 6, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5929, .adv_w = 215, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 5984, .adv_w = 184, .box_w = 12, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6002, .adv_w = 221, .box_w = 7, .box_h = 3, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 6013, .adv_w = 220, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6079, .adv_w = 251, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6190, .adv_w = 210, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6268, .adv_w = 251, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6387, .adv_w = 225, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6471, .adv_w = 130, .box_w = 9, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6548, .adv_w = 254, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6660, .adv_w = 251, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6762, .adv_w = 103, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6796, .adv_w = 105, .box_w = 8, .box_h = 21, .ofs_x = -3, .ofs_y = -4},
    {.bitmap_index = 6880, .adv_w = 227, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6991, .adv_w = 103, .box_w = 3, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7017, .adv_w = 389, .box_w = 21, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7143, .adv_w = 251, .box_w = 12, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7215, .adv_w = 234, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7299, .adv_w = 251, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 7403, .adv_w = 251, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7515, .adv_w = 151, .box_w = 7, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7557, .adv_w = 184, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7623, .adv_w = 152, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7691, .adv_w = 249, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7769, .adv_w = 206, .box_w = 14, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7853, .adv_w = 331, .box_w = 21, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7979, .adv_w = 203, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8057, .adv_w = 206, .box_w = 14, .box_h = 16, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 8169, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8241, .adv_w = 129, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 8315, .adv_w = 110, .box_w = 3, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 8347, .adv_w = 129, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8421, .adv_w = 214, .box_w = 12, .box_h = 4, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 8445, .adv_w = 99, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8445, .adv_w = 368, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8733, .adv_w = 368, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8940, .adv_w = 368, .box_w = 23, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9182, .adv_w = 368, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9389, .adv_w = 253, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9525, .adv_w = 368, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9813, .adv_w = 368, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10089, .adv_w = 414, .box_w = 26, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10362, .adv_w = 368, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10627, .adv_w = 414, .box_w = 26, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10861, .adv_w = 368, .box_w = 23, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11137, .adv_w = 184, .box_w = 12, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11251, .adv_w = 276, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11422, .adv_w = 414, .box_w = 26, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11721, .adv_w = 368, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11928, .adv_w = 253, .box_w = 16, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12120, .adv_w = 322, .box_w = 16, .box_h = 22, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 12296, .adv_w = 322, .box_w = 21, .box_h = 25, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12559, .adv_w = 322, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12780, .adv_w = 322, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13001, .adv_w = 322, .box_w = 16, .box_h = 22, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 13177, .adv_w = 322, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 13408, .adv_w = 230, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13545, .adv_w = 230, .box_w = 13, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13682, .adv_w = 322, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13903, .adv_w = 322, .box_w = 21, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 13956, .adv_w = 414, .box_w = 26, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14190, .adv_w = 460, .box_w = 29, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14538, .adv_w = 414, .box_w = 28, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14874, .adv_w = 368, .box_w = 23, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15127, .adv_w = 322, .box_w = 20, .box_h = 13, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 15257, .adv_w = 322, .box_w = 20, .box_h = 13, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 15387, .adv_w = 460, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15663, .adv_w = 368, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15870, .adv_w = 368, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16135, .adv_w = 368, .box_w = 24, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 16423, .adv_w = 322, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16644, .adv_w = 322, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16896, .adv_w = 322, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17117, .adv_w = 322, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17317, .adv_w = 368, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17524, .adv_w = 230, .box_w = 16, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 17716, .adv_w = 322, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17968, .adv_w = 322, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18220, .adv_w = 414, .box_w = 26, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18454, .adv_w = 368, .box_w = 25, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 18754, .adv_w = 276, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18970, .adv_w = 460, .box_w = 29, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19289, .adv_w = 460, .box_w = 29, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19507, .adv_w = 460, .box_w = 29, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19725, .adv_w = 460, .box_w = 29, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 19943, .adv_w = 460, .box_w = 29, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20161, .adv_w = 460, .box_w = 29, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20379, .adv_w = 460, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20655, .adv_w = 322, .box_w = 18, .box_h = 24, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 20871, .adv_w = 322, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21123, .adv_w = 368, .box_w = 24, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 21411, .adv_w = 460, .box_w = 29, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21672, .adv_w = 276, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21888, .adv_w = 370, .box_w = 24, .box_h = 15, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xef61, 0xef68, 0xef6b, 0xef6c, 0xef6d, 0xef71, 0xef73,
    0xef75, 0xef79, 0xef7c, 0xef81, 0xef86, 0xef87, 0xef88, 0xef9e,
    0xefa3, 0xefa8, 0xefab, 0xefac, 0xefad, 0xefb1, 0xefb2, 0xefb3,
    0xefb4, 0xefc7, 0xefc8, 0xefce, 0xefd0, 0xefd1, 0xefd4, 0xefd7,
    0xefd8, 0xefd9, 0xefdb, 0xeff3, 0xeff5, 0xf024, 0xf025, 0xf027,
    0xf029, 0xf040, 0xf047, 0xf04a, 0xf053, 0xf07c, 0xf084, 0xf0bb,
    0xf14b, 0xf1a0, 0xf1a1, 0xf1a2, 0xf1a3, 0xf1a4, 0xf1e7, 0xf1f3,
    0xf24d, 0xf264, 0xf4ba, 0xf722, 0xf802
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 160, .range_length = 63491, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 61, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 17, 0, 10, -8, 0, 0, 0,
    0, -20, -22, 3, 17, 8, 6, -15,
    3, 18, 1, 15, 4, 12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 22, 3, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 0, 0, 0, 0, -7,
    6, 7, 0, 0, -4, 0, -3, 4,
    0, -4, 0, -4, -2, -7, 0, 0,
    0, 0, -4, 0, 0, -5, -6, 0,
    0, -4, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -4, 0,
    0, -10, 0, -45, 0, 0, -7, 0,
    7, 11, 0, 0, -7, 4, 4, 12,
    7, -6, 7, 0, 0, -21, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -14, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, -18, 0, -15, -3, 0, 0, 0,
    0, 1, 14, 0, -11, -3, -1, 1,
    0, -6, 0, 0, -3, -27, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -29, -3, 14, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 12, 0, 4, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 14, 3, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    3, 7, 4, 11, -4, 0, 0, 7,
    -4, -12, -50, 3, 10, 7, 1, -5,
    0, 13, 0, 12, 0, 12, 0, -34,
    0, -4, 11, 0, 12, -4, 7, 4,
    0, 0, 1, -4, 0, 0, -6, 29,
    0, 29, 0, 11, 0, 15, 5, 6,
    0, 0, 0, -14, 0, 0, 0, 0,
    1, -3, 0, 3, -7, -5, -7, 3,
    0, -4, 0, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -20, 0, -23, 0, 0, 0, 0,
    -3, 0, 36, -4, -5, 4, 4, -3,
    0, -5, 4, 0, 0, -20, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -36, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 22, 0, 0, -14, 0, 12, 0,
    -25, -36, -25, -7, 11, 0, 0, -25,
    0, 4, -8, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 11, -45, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 3,
    3, -4, -7, 0, -1, -1, -4, 0,
    0, -3, 0, 0, 0, -7, 0, -3,
    0, -8, -7, 0, -9, -12, -12, -7,
    0, -7, 0, -7, 0, 0, 0, 0,
    -3, 0, 0, 4, 0, 3, -4, 0,
    0, 0, 0, 4, -3, 0, 0, 0,
    -3, 4, 4, -1, 0, 0, 0, -7,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 5, -3, 0, -4, 0, -6, 0,
    0, -3, 0, 11, 0, 0, -4, 0,
    0, 0, 0, 0, -1, 1, -3, -3,
    0, -4, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    -4, -4, 0, 0, 0, 0, 0, 1,
    0, 0, -3, 0, -4, -4, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -3, -5, 0,
    0, -11, -3, -11, 7, 0, 0, -7,
    4, 7, 10, 0, -9, -1, -4, 0,
    -1, -17, 4, -3, 3, -20, 4, 0,
    0, 1, -19, 0, -20, -3, -32, -3,
    0, -18, 0, 7, 10, 0, 5, 0,
    0, 0, 0, 1, 0, -7, -5, 0,
    0, 0, 0, -4, 0, 0, 0, -4,
    0, 0, 0, 0, 0, -2, -2, 0,
    -2, -5, 0, 0, 0, 0, 0, 0,
    0, -4, -4, 0, -3, -4, -3, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -3, 0, -7, 4, 0, 0, -4,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -4, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -6, 0,
    0, 11, -3, 1, -12, 0, 0, 10,
    -18, -19, -15, -7, 4, 0, -3, -24,
    -7, 0, -7, 0, -7, 6, -7, -24,
    0, -10, 0, 0, 2, -1, 3, -3,
    0, 4, 0, -11, -14, 0, -18, -9,
    -8, -9, -11, -4, -10, -1, -7, -10,
    0, 1, 0, -4, 0, 0, 0, 3,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, 0, -2,
    0, -1, -4, 0, -6, -8, -8, -1,
    0, -11, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 1, -2, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 18, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -7, 0, 0, 0,
    0, -18, -11, 0, 0, 0, -6, -18,
    0, 0, -4, 4, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, 0, 0, 0, 4, 0,
    3, -7, -7, 0, -4, -4, -4, 0,
    0, 0, 0, 0, 0, -11, 0, -4,
    0, -6, -4, 0, -8, -9, -11, -3,
    0, -7, 0, -11, 0, 0, 0, 0,
    29, 0, 0, 2, 0, 0, -5, 0,
    0, -16, 0, 0, 0, 0, 0, -34,
    -7, 12, 11, -3, -15, 0, 4, -6,
    0, -18, -2, -5, 4, -26, -4, 5,
    0, 6, -13, -6, -14, -12, -15, 0,
    0, -22, 0, 21, 0, 0, -2, 0,
    0, 0, -2, -2, -4, -10, -12, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, 0, -2, -4, -6, 0,
    0, -7, 0, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -7, 0, 0, 7,
    -1, 5, 0, -8, 4, -3, -1, -10,
    -4, 0, -5, -4, -3, 0, -6, -6,
    0, 0, -3, -1, -3, -6, -4, 0,
    0, -4, 0, 4, -3, 0, -8, 0,
    0, 0, -7, 0, -6, 0, -6, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 4, 0, -5, 0, -3, -4, -11,
    -3, -3, -3, -1, -3, -4, -1, 0,
    0, 0, 0, 0, -4, -3, -3, 0,
    0, 0, 0, 4, -3, 0, -3, 0,
    0, 0, -3, -4, -3, -3, -4, -3,
    3, 15, -1, 0, -10, 0, -3, 7,
    0, -4, -15, -5, 6, 0, 0, -17,
    -6, 4, -6, 3, 0, -3, -3, -12,
    0, -6, 2, 0, 0, -6, 0, 0,
    0, 4, 4, -7, -7, 0, -6, -4,
    -6, -4, -4, 0, -6, 2, -7, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, 0, -5, 0, 0, -4, -4, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -3, 0,
    0, 0, -6, 0, -7, 0, 0, 0,
    -12, 0, 3, -8, 7, 1, -3, -17,
    0, 0, -8, -4, 0, -15, -9, -10,
    0, 0, -16, -4, -15, -14, -18, 0,
    -10, 0, 3, 25, -5, 0, -8, -4,
    -1, -4, -6, -10, -7, -14, -15, -8,
    0, 0, -3, 0, 1, 0, 0, -26,
    -3, 11, 8, -8, -14, 0, 1, -11,
    0, -18, -3, -4, 7, -34, -5, 1,
    0, 0, -24, -4, -19, -4, -27, 0,
    0, -26, 0, 22, 1, 0, -3, 0,
    0, 0, 0, -2, -3, -14, -3, 0,
    0, 0, 0, 0, -12, 0, -3, 0,
    -1, -10, -17, 0, 0, -2, -6, -11,
    -4, 0, -3, 0, 0, 0, 0, -17,
    -4, -12, -12, -3, -6, -9, -4, -6,
    0, -7, -3, -12, -6, 0, -4, -7,
    -4, -7, 0, 2, 0, -3, -12, 0,
    0, -7, 0, 0, 0, 0, 4, 0,
    3, -7, 15, 0, -4, -4, -4, 0,
    0, 0, 0, 0, 0, -11, 0, -4,
    0, -6, -4, 0, -8, -9, -11, -3,
    0, -7, 3, 15, 0, 0, 0, 0,
    29, 0, 0, 2, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -3, -7,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -4, -4, 0, 0, -7, -4, 0,
    0, -7, 0, 6, -2, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    7, 3, -3, 0, -12, -6, 0, 11,
    -12, -12, -7, -7, 15, 7, 4, -32,
    -3, 7, -4, 0, -4, 4, -4, -13,
    0, -4, 4, -5, -3, -11, -3, 0,
    0, 11, 7, 0, -10, 0, -20, -5,
    11, -5, -14, 1, -5, -12, -12, -4,
    4, 0, -6, 0, -10, 0, 3, 12,
    -8, -14, -15, -9, 11, 0, 1, -27,
    -3, 4, -6, -3, -8, 0, -8, -14,
    -6, -6, -3, 0, 0, -8, -8, -4,
    0, 11, 8, -4, -20, 0, -20, -5,
    0, -13, -21, -1, -12, -6, -12, -10,
    0, 0, -5, 0, -7, -3, 0, -4,
    -7, 0, 6, -12, 4, 0, 0, -20,
    0, -4, -8, -6, -3, -11, -9, -12,
    -8, 0, -11, -4, -8, -7, -11, -4,
    0, 0, 1, 17, -6, 0, -11, -4,
    0, -4, -7, -8, -10, -10, -14, -5,
    7, 0, -6, 0, -18, -4, 2, 7,
    -12, -14, -7, -12, 12, -4, 2, -34,
    -7, 7, -8, -6, -14, 0, -11, -15,
    -4, -4, -3, -4, -8, -11, -1, 0,
    0, 11, 10, -3, -24, 0, -22, -8,
    9, -14, -25, -7, -13, -15, -18, -12,
    0, 0, 0, 0, -4, 0, 0, 4,
    -4, 7, 3, -7, 7, 0, 0, -11,
    -1, 0, -1, 0, 1, 1, -3, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 3, 11, 1, 0, -4, 0,
    0, 0, 0, -3, -3, -4, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 14, 0, 7, 1, 1, -5,
    0, 7, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 0, 10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -22, 0, -4, 6, 0, 11, 0,
    0, 36, 4, -7, -7, 4, 4, -3,
    1, -18, 0, 0, 18, -22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 14, 52, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, -7, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -10, 0, 0, 1, 0,
    0, 4, 47, -7, -3, 12, 10, -10,
    4, 0, 0, 4, 4, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -48, 10, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, -10,
    0, 0, 0, 0, -8, -2, 0, 0,
    0, -8, 0, -4, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -4, 0, 0,
    0, -6, 0, -10, 0, 0, 0, -6,
    4, -4, 0, 0, -10, -4, -8, 0,
    0, -10, 0, -4, 0, -17, 0, -4,
    0, 0, -30, -7, -15, -4, -13, 0,
    0, -25, 0, -10, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -6, -7, -3,
    0, 0, 0, 0, -8, 0, -8, 5,
    -4, 7, 0, -3, -8, -3, -6, -7,
    0, -4, -2, -3, 3, -10, -1, 0,
    0, 0, -32, -3, -5, 0, -8, 0,
    -3, -17, -3, 0, 0, -3, -3, 0,
    0, 0, 0, 3, 0, -3, -6, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 5, 0, 0, 0, 0,
    0, -8, 0, -3, 0, 0, 0, -7,
    4, 0, 0, 0, -10, -4, -7, 0,
    0, -10, 0, -4, 0, -17, 0, 0,
    0, 0, -36, 0, -7, -14, -18, 0,
    0, -25, 0, -3, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -6, -2,
    1, 0, 0, 6, -5, 0, 11, 18,
    -4, -4, -11, 4, 18, 6, 8, -10,
    4, 15, 4, 11, 8, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 23, 17, -7, -4, 0, -3, 29,
    16, 29, 0, 0, 0, 4, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -31, -4, -3, -15, -18, 0,
    0, -25, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -31, -4, -3, -15, -18, 0,
    0, -15, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -8, 4, 0, -4, 3, 7, 4, -11,
    0, -1, -3, 4, 0, 3, 0, 0,
    0, 0, -9, 0, -3, -3, -7, 0,
    -3, -15, 0, 23, -4, 0, -8, -3,
    0, -3, -6, 0, -4, -10, -7, -4,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 5, 0, 0,
    0, 0, -31, -4, -3, -15, -18, 0,
    0, -25, 0, 0, 0, 0, 0, 0,
    18, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -6, 0, -12, -4, -3, 11,
    -3, -4, -15, 1, -2, 1, -3, -10,
    1, 8, 1, 3, 1, 3, -9, -15,
    -4, 0, -14, -7, -10, -15, -14, 0,
    -6, -7, -4, -5, -3, -3, -4, -3,
    0, -3, -1, 6, 0, 6, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -4, -4, 0,
    0, -10, 0, -2, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -22, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -4, 0,
    0, 0, 0, 0, -3, 0, 0, -6,
    -4, 4, 0, -6, -7, -3, 0, -11,
    -3, -8, -3, -4, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 12, 0, 0, -7, 0,
    0, 0, 0, -5, 0, -4, 0, 0,
    0, 0, -3, 0, -8, 0, 0, 15,
    -5, -12, -11, 3, 4, 4, -1, -10,
    3, 6, 3, 11, 3, 12, -3, -10,
    0, 0, -15, 0, 0, -11, -10, 0,
    0, -7, 0, -5, -6, 0, -6, 0,
    -6, 0, -3, 6, 0, -3, -11, -4,
    0, 0, -3, 0, -7, 0, 0, 5,
    -8, 0, 4, -4, 3, 0, 0, -12,
    0, -3, -1, 0, -4, 4, -3, 0,
    0, 0, -15, -4, -8, 0, -11, 0,
    0, -17, 0, 14, -4, 0, -7, 0,
    2, 0, -4, 0, -4, -11, 0, -4,
    0, 0, 0, 0, -3, 0, 0, 4,
    -5, 1, 0, 0, -4, -3, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 8, 0, 0, -3, 0,
    0, 0, 0, 1, 0, -4, -4, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserratMedium_23 = {
#else
lv_font_t lv_font_montserratMedium_23 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 23,          /*The maximum line height required by the font  default: (f.src.ascent - f.src.descent)*/
    .base_line = 3,                          /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_23*/

