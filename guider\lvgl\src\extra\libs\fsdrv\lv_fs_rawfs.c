/*
 * @file lv_fs_rawfs.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "../../../lvgl.h"

#if LV_USE_FS_RAWFS

/*********************
 *      DEFINES
 *********************/

#if LV_FS_RAWFS_LETTER == '\0'
    #error "LV_FS_RAWFS_LETTER must be an upper case ASCII letter"
#endif

#if LV_FS_RAWFS_XIP
    #if LV_FS_RAWFS_XIP_BASE_ADDR == 0xFFFFFFFF
        #error "Base address for image binary (LV_FS_RAWFS_XIP_BASE_ADDR) must be valid"
    #endif
#endif

/**********************
 *      TYPEDEFS
 **********************/
/* Info for raw binaries: base, offset, size, name*/
/*
const rawfs_size_t rawfs_file_count = 3;
rawfs_file_t rawfs_files[3] = {
    0x0, 0, 1688704, "/apic33695.bin",
    0x19c480, 0, 1204, "/green_left_icon.bin",
    0x19c934, 0, 1204, "/red_right_icon.bin",
};
*/
extern rawfs_size_t rawfs_file_count;
extern rawfs_file_t rawfs_files[];

/**********************
 *  STATIC PROTOTYPES
 **********************/
static lv_fs_res_t rawfs_file_find(const char * path, rawfs_file_t * file_p);

static void fs_init(void);

static void * fs_open(lv_fs_drv_t * drv, const char * path, lv_fs_mode_t mode);
static lv_fs_res_t fs_close(lv_fs_drv_t * drv, void * file_p);
static lv_fs_res_t fs_read(lv_fs_drv_t * drv, void * file_p, void * buf, uint32_t btr, uint32_t * br);
static lv_fs_res_t fs_write(lv_fs_drv_t * drv, void * file_p, const void * buf, uint32_t btw, uint32_t * bw);
static lv_fs_res_t fs_seek(lv_fs_drv_t * drv, void * file_p, uint32_t pos, lv_fs_whence_t whence);
static lv_fs_res_t fs_size(lv_fs_drv_t * drv, void * file_p, uint32_t * size_p);
static lv_fs_res_t fs_tell(lv_fs_drv_t * drv, void * file_p, uint32_t * pos_p);

static void * fs_dir_open(lv_fs_drv_t * drv, const char * path);
static lv_fs_res_t fs_dir_read(lv_fs_drv_t * drv, void * rddir_p, char * fn);
static lv_fs_res_t fs_dir_close(lv_fs_drv_t * drv, void * rddir_p);

/**********************
 *  STATIC VARIABLES
 **********************/
static rawfs_file_t FIL;

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_fs_rawfs_init(void)
{
    /*----------------------------------------------------
     * Initialize your storage device and File System
     * -------------------------------------------------*/
    fs_init();

    /*---------------------------------------------------
     * Register the file system interface in LVGL
     *--------------------------------------------------*/

    /*Add a simple drive to open images*/
    static lv_fs_drv_t fs_drv;
    lv_fs_drv_init(&fs_drv);

    /*Set up fields...*/
    fs_drv.letter = LV_FS_RAWFS_LETTER;
    fs_drv.open_cb = fs_open;
    fs_drv.close_cb = fs_close;
    fs_drv.read_cb = fs_read;
    fs_drv.write_cb = fs_write;
    fs_drv.seek_cb = fs_seek;
    fs_drv.tell_cb = fs_tell;

    fs_drv.dir_close_cb = fs_dir_close;
    fs_drv.dir_open_cb = fs_dir_open;
    fs_drv.dir_read_cb = fs_dir_read;

    lv_fs_drv_register(&fs_drv);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/**
 * Find file based on path
 * @param path      path to the file (e.g. /folder/file.txt)
 * @param file_p    pointer to a file_t variable. (opened with fs_open)
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t rawfs_file_find(const char * path, rawfs_file_t * file_p)
{
    lv_fs_res_t res = LV_FS_RES_FS_ERR;
    /*Find file*/
    for(int i = 0; i < rawfs_file_count; i++) {
        if(0 == strcmp(path, rawfs_files[i].name)) {
            *file_p = rawfs_files[i];
            res = LV_FS_RES_OK;
            break;
        }
    }
    return res;
}

/*Initialize your Storage device and File system.*/
static void fs_init(void)
{
    FIL.base = 0;
    FIL.offset = 0;
}

/**
 * Open a file
 * @param drv       pointer to a driver where this function belongs
 * @param path      path to the file (e.g. /folder/file.txt)
 * @param mode      read: FS_MODE_RD, write: FS_MODE_WR, both: FS_MODE_RD | FS_MODE_WR
 * @return          a file descriptor or NULL on error
 */
static void * fs_open(lv_fs_drv_t * drv, const char * path, lv_fs_mode_t mode)
{
    lv_fs_res_t res = LV_FS_RES_NOT_IMP;

    void * f = NULL;

    if(mode == LV_FS_MODE_RD) {
        /*Open a file for read*/
        res = rawfs_file_find(path, &FIL);
    }
    else if(mode == (LV_FS_MODE_WR | LV_FS_MODE_RD)) {
        /*Open a file for read and write*/
        res = rawfs_file_find(path, &FIL);
    }

    if(res == LV_FS_RES_OK) {
        f = &FIL;
    }

    return f;
}

/**
 * Close an opened file
 * @param drv       pointer to a driver where this function belongs
 * @param file_p    pointer to a file_t variable. (opened with fs_open)
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_close(lv_fs_drv_t * drv, void * file_p)
{
    lv_fs_res_t res = LV_FS_RES_OK;

    fs_init();

    return res;
}

/**
 * Read data from an opened file
 * @param drv       pointer to a driver where this function belongs
 * @param file_p    pointer to a file_t variable.
 * @param buf       pointer to a memory block where to store the read data
 * @param btr       number of Bytes To Read
 * @param br        the real number of read bytes (Byte Read)
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_read(lv_fs_drv_t * drv, void * file_p, void * buf, uint32_t btr, uint32_t * br)
{
    lv_fs_res_t res = LV_FS_RES_OK;

    rawfs_file_t * p = (rawfs_file_t *)file_p;
#if LV_FS_RAWFS_XIP
    /*For XIP flash, copy directly*/
    lv_memcpy((uint8_t *)buf, (uint8_t *)(LV_FS_RAWFS_XIP_BASE_ADDR + p->base + p->offset), btr);
#else
    /*For Non-XIP flash, use driver API*/
    //W25QXX_Read((unit8_t *)buf, (unit8_t *)(p->base + p->offset), btr);
    res = LV_FS_RES_NOT_IMP;
#endif
    p->offset += btr;
    *br = btr;

    return res;
}

/**
 * Write into a file
 * @param drv       pointer to a driver where this function belongs
 * @param file_p    pointer to a file_t variable
 * @param buf       pointer to a buffer with the bytes to write
 * @param btw       Bytes To Write
 * @param bw        the number of real written bytes (Bytes Written). NULL if unused.
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_write(lv_fs_drv_t * drv, void * file_p, const void * buf, uint32_t btw, uint32_t * bw)
{
    lv_fs_res_t res = LV_FS_RES_NOT_IMP;

    /*Add your code here*/

    return res;
}

/**
 * Set the read write pointer. Also expand the file size if necessary.
 * @param drv       pointer to a driver where this function belongs
 * @param file_p    pointer to a file_t variable. (opened with fs_open )
 * @param pos       the new position of read write pointer
 * @param whence    tells from where to interpret the `pos`. See @lv_fs_whence_t
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_seek(lv_fs_drv_t * drv, void * file_p, uint32_t pos, lv_fs_whence_t whence)
{
    lv_fs_res_t res = LV_FS_RES_OK;

    rawfs_file_t * p = (rawfs_file_t *)file_p;
    if(whence == LV_FS_SEEK_SET) {
        p->offset = pos;
    }
    else if(whence == LV_FS_SEEK_CUR) {
        p->offset += pos;
    }
    else if(whence == LV_FS_SEEK_END) {
        p->offset = p->size + pos;
    }
    else {
        res = LV_FS_RES_NOT_IMP;
    }

    return res;
}

/**
 * Give the position of the read write pointer
 * @param drv       pointer to a driver where this function belongs
 * @param file_p    pointer to a file_t variable.
 * @param pos_p     pointer to to store the result
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_tell(lv_fs_drv_t * drv, void * file_p, uint32_t * pos_p)
{
    lv_fs_res_t res = LV_FS_RES_OK;

    rawfs_file_t * p = (rawfs_file_t *)file_p;
    *pos_p = p->offset;

    return res;
}

/**
 * Initialize a 'lv_fs_dir_t' variable for directory reading
 * @param drv       pointer to a driver where this function belongs
 * @param path      path to a directory
 * @return          pointer to the directory read descriptor or NULL on error
 */
static void * fs_dir_open(lv_fs_drv_t * drv, const char * path)
{
    void * dir = NULL;
    /*Add your code here*/
    return dir;
}

/**
 * Read the next filename form a directory.
 * The name of the directories will begin with '/'
 * @param drv       pointer to a driver where this function belongs
 * @param rddir_p   pointer to an initialized 'lv_fs_dir_t' variable
 * @param fn        pointer to a buffer to store the filename
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_dir_read(lv_fs_drv_t * drv, void * rddir_p, char * fn)
{
    lv_fs_res_t res = LV_FS_RES_NOT_IMP;

    /*Add your code here*/

    return res;
}

/**
 * Close the directory reading
 * @param drv       pointer to a driver where this function belongs
 * @param rddir_p   pointer to an initialized 'lv_fs_dir_t' variable
 * @return          LV_FS_RES_OK: no error or  any error from @lv_fs_res_t enum
 */
static lv_fs_res_t fs_dir_close(lv_fs_drv_t * drv, void * rddir_p)
{
    lv_fs_res_t res = LV_FS_RES_NOT_IMP;

    /*Add your code here*/

    return res;
}

#endif /*LV_USE_FS_RAWFS*/

