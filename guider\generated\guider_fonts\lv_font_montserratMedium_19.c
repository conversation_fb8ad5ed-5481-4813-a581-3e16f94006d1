/*
 * Copyright 2025 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */
/*******************************************************************************
 * Size: 19 px
 * Bpp: 4
 * Opts: --user-data-dir=C:\Users\<USER>\AppData\Roaming\gui-guider --app-path=D:\GUI_Guider\Gui-Guider\resources\app.asar --no-sandbox --no-zygote --lang=zh-CN --device-scale-factor=1.25 --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=5 --time-ticks-at-unix-epoch=-1755411396113839 --launch-time-ticks=857559356 --mojo-platform-channel-handle=2860 --field-trial-handle=1716,i,5431673224908323737,3035983149899318021,131072 --disable-features=SpareRendererForSitePerProcess,WinRetrieveSuggestionsOnlyOnDemand /prefetch:1
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRATMEDIUM_19
#define LV_FONT_MONTSERRATMEDIUM_19 1
#endif

#if LV_FONT_MONTSERRATMEDIUM_19

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x7f, 0x97, 0xf9, 0x6f, 0x86, 0xf7, 0x5f, 0x74,
    0xf6, 0x4f, 0x53, 0xf5, 0x2f, 0x40, 0x0, 0x5,
    0x19, 0xfb, 0x6f, 0x70,

    /* U+0022 "\"" */
    0xcc, 0x5, 0xf3, 0xcc, 0x5, 0xf3, 0xbb, 0x4,
    0xf2, 0xbb, 0x4, 0xf2, 0xba, 0x4, 0xf1, 0x0,
    0x0, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0xba, 0x0, 0xc, 0x90, 0x0, 0x0,
    0xd, 0x80, 0x0, 0xe6, 0x0, 0x0, 0x0, 0xf5,
    0x0, 0xf, 0x40, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x5, 0x68, 0xf6, 0x66, 0x9f, 0x66,
    0x50, 0x0, 0x5f, 0x0, 0x7, 0xe0, 0x0, 0x0,
    0x8, 0xd0, 0x0, 0x9c, 0x0, 0x0, 0x11, 0xab,
    0x11, 0x1b, 0xa1, 0x10, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x32, 0x44, 0xe9, 0x44, 0x4f, 0x84,
    0x40, 0x0, 0xf, 0x50, 0x1, 0xf4, 0x0, 0x0,
    0x1, 0xf3, 0x0, 0x2f, 0x20, 0x0, 0x0, 0x3f,
    0x10, 0x4, 0xf0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x4, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0x90,
    0x0, 0x0, 0x0, 0x19, 0xdf, 0xfe, 0xb6, 0x0,
    0x2, 0xef, 0xdd, 0xdc, 0xff, 0x40, 0xa, 0xf7,
    0x9, 0x90, 0x6, 0x0, 0xd, 0xf0, 0x9, 0x90,
    0x0, 0x0, 0xb, 0xf6, 0x9, 0x90, 0x0, 0x0,
    0x3, 0xff, 0xcd, 0xa0, 0x0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xa3, 0x0, 0x0, 0x0, 0xa, 0xdc,
    0xff, 0x40, 0x0, 0x0, 0x9, 0x90, 0x5f, 0xd0,
    0x0, 0x0, 0x9, 0x90, 0xe, 0xf0, 0xa, 0x50,
    0x9, 0x90, 0x4f, 0xc0, 0x1d, 0xfe, 0xad, 0xdc,
    0xff, 0x30, 0x0, 0x6b, 0xef, 0xfe, 0xa2, 0x0,
    0x0, 0x0, 0x9, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x90, 0x0, 0x0,

    /* U+0025 "%" */
    0x1, 0xae, 0xd6, 0x0, 0x0, 0x8, 0xe0, 0x0,
    0xb, 0xb2, 0x4e, 0x50, 0x0, 0x3f, 0x30, 0x0,
    0x2f, 0x10, 0x8, 0xb0, 0x0, 0xe8, 0x0, 0x0,
    0x4f, 0x0, 0x6, 0xd0, 0xa, 0xc0, 0x0, 0x0,
    0x2f, 0x10, 0x7, 0xb0, 0x5f, 0x20, 0x0, 0x0,
    0xc, 0x90, 0x2e, 0x61, 0xe7, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0xf8, 0xb, 0xb0, 0x5c, 0xd9, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x14, 0xf5, 0x3b, 0xb0,
    0x0, 0x0, 0x2, 0xf5, 0xb, 0x80, 0x1, 0xf2,
    0x0, 0x0, 0xc, 0xa0, 0xd, 0x60, 0x0, 0xf4,
    0x0, 0x0, 0x8e, 0x10, 0xb, 0x70, 0x1, 0xf2,
    0x0, 0x3, 0xf4, 0x0, 0x5, 0xe2, 0x9, 0xc0,
    0x0, 0xd, 0x80, 0x0, 0x0, 0x6d, 0xea, 0x10,

    /* U+0026 "&" */
    0x0, 0x3, 0xbe, 0xfb, 0x30, 0x0, 0x0, 0x2,
    0xfd, 0x65, 0xcf, 0x10, 0x0, 0x0, 0x6f, 0x40,
    0x3, 0xf5, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x1e, 0xe3, 0x9f, 0x90, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x4f, 0xc1,
    0x3e, 0xe3, 0x8, 0xe0, 0xd, 0xe0, 0x0, 0x2e,
    0xf4, 0xec, 0x1, 0xfb, 0x0, 0x0, 0x2d, 0xff,
    0x40, 0xf, 0xe2, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x6f, 0xfa, 0x89, 0xdf, 0xac, 0xf5, 0x0, 0x4b,
    0xef, 0xeb, 0x40, 0xb, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xcc, 0xcc, 0xbb, 0xbb, 0xba, 0x0,

    /* U+0028 "(" */
    0x0, 0x3f, 0x80, 0xb, 0xf1, 0x1, 0xfa, 0x0,
    0x6f, 0x50, 0xb, 0xf1, 0x0, 0xde, 0x0, 0xf,
    0xb0, 0x1, 0xfa, 0x0, 0x2f, 0x90, 0x2, 0xf9,
    0x0, 0x1f, 0xa0, 0x0, 0xfb, 0x0, 0xd, 0xe0,
    0x0, 0xbf, 0x10, 0x6, 0xf5, 0x0, 0x1f, 0xa0,
    0x0, 0xbf, 0x10, 0x3, 0xf8,

    /* U+0029 ")" */
    0x2f, 0x90, 0x0, 0xaf, 0x20, 0x4, 0xf8, 0x0,
    0xe, 0xd0, 0x0, 0xaf, 0x10, 0x7, 0xf4, 0x0,
    0x5f, 0x70, 0x4, 0xf8, 0x0, 0x3f, 0x90, 0x3,
    0xf9, 0x0, 0x4f, 0x80, 0x5, 0xf7, 0x0, 0x7f,
    0x40, 0xa, 0xf1, 0x0, 0xed, 0x0, 0x4f, 0x80,
    0xa, 0xf2, 0x2, 0xf9, 0x0,

    /* U+002A "*" */
    0x0, 0xc, 0x50, 0x0, 0x37, 0xc, 0x52, 0x90,
    0x3d, 0xee, 0xcf, 0xa0, 0x0, 0xcf, 0xf6, 0x0,
    0x4d, 0xdd, 0xbf, 0xa0, 0x37, 0xc, 0x51, 0x80,
    0x0, 0xb, 0x50, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x4f, 0x50,
    0x0, 0x0, 0x4, 0xf5, 0x0, 0x1, 0x22, 0x5f,
    0x62, 0x21, 0xbf, 0xff, 0xff, 0xff, 0xc3, 0x55,
    0x8f, 0x85, 0x54, 0x0, 0x4, 0xf5, 0x0, 0x0,
    0x0, 0x4f, 0x50, 0x0, 0x0, 0x4, 0xf5, 0x0,
    0x0,

    /* U+002C "," */
    0x1, 0x20, 0xe, 0xf3, 0xe, 0xf5, 0x6, 0xf1,
    0x9, 0xb0, 0xe, 0x50,

    /* U+002D "-" */
    0x88, 0x88, 0x81, 0xef, 0xff, 0xf3,

    /* U+002E "." */
    0x5, 0x70, 0x1f, 0xf5, 0xc, 0xe2,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x28, 0x20, 0x0, 0x0, 0xa,
    0xf0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0,
    0x5f, 0x50, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x1, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0x30, 0x0,
    0x0, 0xc, 0xe0, 0x0, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0xd, 0xc0,
    0x0, 0x0, 0x3, 0xf7, 0x0, 0x0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0xe, 0xb0, 0x0, 0x0, 0x4,
    0xf6, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x3, 0xae, 0xfd, 0x81, 0x0, 0x0, 0x5f,
    0xfc, 0xad, 0xfd, 0x10, 0x1, 0xff, 0x30, 0x0,
    0x8f, 0xb0, 0x8, 0xf6, 0x0, 0x0, 0xc, 0xf3,
    0xd, 0xf0, 0x0, 0x0, 0x6, 0xf8, 0xf, 0xd0,
    0x0, 0x0, 0x3, 0xfa, 0x1f, 0xd0, 0x0, 0x0,
    0x2, 0xfb, 0xf, 0xd0, 0x0, 0x0, 0x3, 0xfa,
    0xd, 0xf0, 0x0, 0x0, 0x6, 0xf8, 0x8, 0xf6,
    0x0, 0x0, 0xc, 0xf3, 0x1, 0xff, 0x40, 0x0,
    0x8f, 0xb0, 0x0, 0x5f, 0xfc, 0xbe, 0xfd, 0x10,
    0x0, 0x3, 0xae, 0xfd, 0x81, 0x0,

    /* U+0031 "1" */
    0xdf, 0xff, 0xf0, 0x8a, 0xaf, 0xf0, 0x0, 0xd,
    0xf0, 0x0, 0xd, 0xf0, 0x0, 0xd, 0xf0, 0x0,
    0xd, 0xf0, 0x0, 0xd, 0xf0, 0x0, 0xd, 0xf0,
    0x0, 0xd, 0xf0, 0x0, 0xd, 0xf0, 0x0, 0xd,
    0xf0, 0x0, 0xd, 0xf0, 0x0, 0xd, 0xf0,

    /* U+0032 "2" */
    0x1, 0x8c, 0xff, 0xd8, 0x10, 0x4, 0xff, 0xdb,
    0xbe, 0xfd, 0x10, 0x3d, 0x40, 0x0, 0xb, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x30, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0x0, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x1, 0xcf, 0x70, 0x0, 0x0, 0x1,
    0xdf, 0x60, 0x0, 0x0, 0x1, 0xef, 0xda, 0xaa,
    0xaa, 0xa2, 0x4f, 0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+0033 "3" */
    0x4f, 0xff, 0xff, 0xff, 0xf7, 0x2, 0xaa, 0xaa,
    0xaa, 0xff, 0x40, 0x0, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0xf8, 0x30,
    0x0, 0x0, 0x3, 0xde, 0xff, 0xb1, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x5a,
    0x20, 0x0, 0x8, 0xfa, 0x9, 0xff, 0xdb, 0xbe,
    0xfe, 0x20, 0x4, 0xad, 0xff, 0xd8, 0x10, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xef, 0x20, 0x9, 0xb0, 0x0, 0x0,
    0xcf, 0x40, 0x0, 0xdf, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0xd, 0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x72, 0x99, 0x99, 0x99, 0x9f, 0xf9,
    0x94, 0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x2f, 0xda,
    0xaa, 0xaa, 0x50, 0x4, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x7, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfd, 0x92,
    0x0, 0x6, 0xaa, 0xab, 0xdf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x30, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x2c,
    0x30, 0x0, 0x5, 0xfe, 0x6, 0xff, 0xeb, 0xbd,
    0xff, 0x40, 0x2, 0x8d, 0xff, 0xe9, 0x20, 0x0,

    /* U+0036 "6" */
    0x0, 0x1, 0x7c, 0xef, 0xd9, 0x20, 0x0, 0x2d,
    0xfe, 0xaa, 0xbf, 0x20, 0x0, 0xdf, 0x60, 0x0,
    0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xf, 0xd1,
    0x9d, 0xfe, 0xa3, 0x0, 0x1f, 0xed, 0xe9, 0x8b,
    0xff, 0x40, 0xf, 0xfd, 0x10, 0x0, 0x4f, 0xe0,
    0xe, 0xf5, 0x0, 0x0, 0xb, 0xf2, 0xa, 0xf5,
    0x0, 0x0, 0xb, 0xf1, 0x3, 0xfd, 0x10, 0x0,
    0x4f, 0xd0, 0x0, 0x7f, 0xea, 0x8b, 0xff, 0x30,
    0x0, 0x4, 0xbe, 0xfe, 0xa2, 0x0,

    /* U+0037 "7" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x97, 0xfc, 0xaa,
    0xaa, 0xad, 0xf7, 0x7f, 0x60, 0x0, 0x0, 0xef,
    0x16, 0xf5, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x0, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0, 0x2, 0xef,
    0xc8, 0x8b, 0xff, 0x50, 0x9, 0xf7, 0x0, 0x0,
    0x4f, 0xd0, 0x9, 0xf4, 0x0, 0x0, 0xf, 0xd0,
    0x3, 0xfd, 0x52, 0x24, 0xcf, 0x60, 0x0, 0x4f,
    0xff, 0xff, 0xf8, 0x0, 0x3, 0xff, 0x95, 0x58,
    0xef, 0x70, 0xd, 0xf3, 0x0, 0x0, 0x1e, 0xf1,
    0x1f, 0xd0, 0x0, 0x0, 0x9, 0xf5, 0x1f, 0xd0,
    0x0, 0x0, 0xa, 0xf5, 0xc, 0xf6, 0x0, 0x0,
    0x3f, 0xf1, 0x2, 0xef, 0xc9, 0x9b, 0xff, 0x50,
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0,

    /* U+0039 "9" */
    0x0, 0x5b, 0xef, 0xd9, 0x10, 0x0, 0x8f, 0xe9,
    0x8a, 0xfe, 0x30, 0x2f, 0xd0, 0x0, 0x2, 0xee,
    0x6, 0xf7, 0x0, 0x0, 0x9, 0xf5, 0x5f, 0x90,
    0x0, 0x0, 0xbf, 0xa1, 0xef, 0x71, 0x2, 0x9f,
    0xfc, 0x3, 0xef, 0xff, 0xfe, 0x6f, 0xc0, 0x0,
    0x47, 0x75, 0x3, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x80, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0,
    0x10, 0x0, 0x1b, 0xf9, 0x0, 0x6f, 0xba, 0xbf,
    0xfb, 0x0, 0x4, 0xbe, 0xfe, 0xb5, 0x0, 0x0,

    /* U+003A ":" */
    0xb, 0xe2, 0x1f, 0xf5, 0x5, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x70,
    0x1f, 0xf5, 0xc, 0xe2,

    /* U+003B ";" */
    0xb, 0xe2, 0x1f, 0xf5, 0x5, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20,
    0xe, 0xf3, 0xe, 0xf5, 0x6, 0xf1, 0x9, 0xb0,
    0xe, 0x50,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0, 0x5c,
    0xfc, 0x0, 0x39, 0xff, 0xc6, 0x5, 0xdf, 0xe8,
    0x20, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x4, 0xbf,
    0xf9, 0x30, 0x0, 0x0, 0x28, 0xef, 0xd7, 0x10,
    0x0, 0x0, 0x4b, 0xfc, 0x0, 0x0, 0x0, 0x1,
    0x50,

    /* U+003D "=" */
    0xbf, 0xff, 0xff, 0xff, 0xc5, 0x77, 0x77, 0x77,
    0x75, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x1b, 0xff,
    0xff, 0xff, 0xfc, 0x35, 0x55, 0x55, 0x55, 0x40,

    /* U+003E ">" */
    0x52, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x60, 0x0,
    0x0, 0x6, 0xcf, 0xf9, 0x30, 0x0, 0x0, 0x28,
    0xef, 0xd6, 0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x39, 0xef, 0xc5, 0x17, 0xdf, 0xe8, 0x20, 0xb,
    0xfb, 0x50, 0x0, 0x0, 0x51, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x1, 0x8d, 0xff, 0xd8, 0x10, 0x4f, 0xfc, 0x9a,
    0xef, 0xe1, 0x4c, 0x30, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0x0, 0x6, 0xf8, 0x0, 0x0, 0x0, 0xa,
    0xf4, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x9, 0xfa, 0x0, 0x0, 0x0, 0x5f, 0xb0, 0x0,
    0x0, 0x0, 0x7d, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x25, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x9e, 0x30,
    0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0xa5, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x96, 0x44, 0x6a,
    0xfe, 0x40, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x70, 0x0, 0x7, 0xf3, 0x0,
    0x8d, 0xfd, 0x81, 0xf9, 0x5f, 0x40, 0x2, 0xf6,
    0x1, 0xdf, 0xb7, 0x8e, 0xdf, 0x90, 0x8e, 0x0,
    0x9e, 0x0, 0x9f, 0x60, 0x0, 0x1d, 0xf9, 0x1,
    0xf5, 0xe, 0x90, 0xf, 0xc0, 0x0, 0x0, 0x4f,
    0x90, 0xc, 0x90, 0xf6, 0x2, 0xf9, 0x0, 0x0,
    0x1, 0xf9, 0x0, 0xab, 0x1f, 0x50, 0x2f, 0x90,
    0x0, 0x0, 0x1f, 0x90, 0x9, 0xa0, 0xf6, 0x0,
    0xfc, 0x0, 0x0, 0x5, 0xf9, 0x0, 0xb9, 0xd,
    0x90, 0x9, 0xf6, 0x0, 0x1, 0xdf, 0xa0, 0xf,
    0x50, 0x9e, 0x0, 0xd, 0xfb, 0x78, 0xec, 0xdf,
    0x7c, 0xd0, 0x2, 0xf6, 0x0, 0x8, 0xdf, 0xe8,
    0x3, 0xdf, 0xc2, 0x0, 0x7, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfe, 0x96, 0x45, 0x7c, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7c, 0xef, 0xed, 0xa4, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf9, 0xcf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x24, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xb0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xf3, 0x0, 0x5f, 0x70, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x7f, 0x40,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x6, 0xfa, 0x88, 0x88,
    0x88, 0xbf, 0x40, 0x0, 0xdf, 0x0, 0x0, 0x0,
    0x1, 0xfc, 0x0, 0x5f, 0x90, 0x0, 0x0, 0x0,
    0xb, 0xf3, 0xc, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xb0,

    /* U+0042 "B" */
    0xff, 0xff, 0xff, 0xfd, 0x92, 0x0, 0xff, 0x88,
    0x88, 0x9c, 0xff, 0x20, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0x90, 0xfe, 0x0, 0x0, 0x0, 0x3f, 0xb0,
    0xfe, 0x0, 0x0, 0x0, 0x8f, 0x70, 0xff, 0x88,
    0x88, 0x9c, 0xfb, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0xfe, 0x0, 0x0, 0x2, 0x8f, 0xd0,
    0xfe, 0x0, 0x0, 0x0, 0xa, 0xf4, 0xfe, 0x0,
    0x0, 0x0, 0x8, 0xf6, 0xfe, 0x0, 0x0, 0x0,
    0x1d, 0xf4, 0xff, 0x88, 0x88, 0x8a, 0xef, 0xb0,
    0xff, 0xff, 0xff, 0xfe, 0xc6, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x39, 0xdf, 0xfd, 0x92, 0x0, 0x0,
    0x9, 0xff, 0xeb, 0xbd, 0xff, 0x70, 0x0, 0xaf,
    0xd4, 0x0, 0x0, 0x3d, 0x70, 0x5, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xd4, 0x0, 0x0, 0x4d, 0x80, 0x0, 0x9, 0xff,
    0xeb, 0xbe, 0xff, 0x60, 0x0, 0x0, 0x39, 0xdf,
    0xfd, 0x92, 0x0,

    /* U+0044 "D" */
    0xff, 0xff, 0xff, 0xec, 0x71, 0x0, 0xf, 0xfa,
    0xaa, 0xab, 0xef, 0xf6, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x6e, 0xf6, 0xf, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0xf1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x4, 0xfa, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xbf, 0xe0, 0x0,
    0x0, 0x0, 0x4, 0xfa, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xf1, 0xfe, 0x0, 0x0, 0x0, 0x5e, 0xf6, 0xf,
    0xfa, 0xaa, 0xab, 0xef, 0xf6, 0x0, 0xff, 0xff,
    0xff, 0xec, 0x71, 0x0, 0x0,

    /* U+0045 "E" */
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xaa, 0xaa,
    0xaa, 0xa3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x99, 0x99, 0x99, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xaa, 0xaa, 0xaa, 0xa6, 0xff, 0xff, 0xff, 0xff,
    0xfa,

    /* U+0046 "F" */
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xaa, 0xaa,
    0xaa, 0xa3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0x90, 0xff, 0x99, 0x99, 0x99, 0x50,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x93, 0x0, 0x0,
    0x8, 0xff, 0xeb, 0xbd, 0xff, 0x80, 0x0, 0xaf,
    0xd4, 0x0, 0x0, 0x2b, 0x90, 0x5, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x7,
    0x90, 0xf, 0xf0, 0x0, 0x0, 0x0, 0xc, 0xf0,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0xc, 0xf0, 0x4,
    0xfd, 0x10, 0x0, 0x0, 0xc, 0xf0, 0x0, 0x9f,
    0xd4, 0x0, 0x0, 0x3e, 0xf0, 0x0, 0x8, 0xff,
    0xeb, 0xbd, 0xff, 0xa0, 0x0, 0x0, 0x39, 0xdf,
    0xfd, 0xa3, 0x0,

    /* U+0048 "H" */
    0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7, 0xfe, 0x0,
    0x0, 0x0, 0x7, 0xf7, 0xfe, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7, 0xff, 0xaa,
    0xaa, 0xaa, 0xac, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7, 0xfe, 0x0,
    0x0, 0x0, 0x7, 0xf7, 0xfe, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0xfe, 0x0, 0x0, 0x0, 0x7, 0xf7,

    /* U+0049 "I" */
    0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe,
    0xfe, 0xfe, 0xfe, 0xfe, 0xfe,

    /* U+004A "J" */
    0x0, 0xdf, 0xff, 0xff, 0xd0, 0x8, 0xaa, 0xaa,
    0xfd, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xf, 0xd0,
    0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0x0, 0x0, 0x1, 0xfc, 0x9, 0x80, 0x0,
    0x8f, 0x90, 0xcf, 0xda, 0xcf, 0xf2, 0x0, 0x7d,
    0xfe, 0xb3, 0x0,

    /* U+004B "K" */
    0xfe, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0xfe, 0x0,
    0x0, 0x6, 0xfc, 0x0, 0xfe, 0x0, 0x0, 0x6f,
    0xc0, 0x0, 0xfe, 0x0, 0x6, 0xfc, 0x10, 0x0,
    0xfe, 0x0, 0x5f, 0xd1, 0x0, 0x0, 0xfe, 0x5,
    0xfd, 0x10, 0x0, 0x0, 0xfe, 0x5f, 0xff, 0x30,
    0x0, 0x0, 0xff, 0xfe, 0x8f, 0xe1, 0x0, 0x0,
    0xff, 0xe2, 0x8, 0xfc, 0x0, 0x0, 0xff, 0x20,
    0x0, 0xaf, 0xa0, 0x0, 0xfe, 0x0, 0x0, 0xb,
    0xf8, 0x0, 0xfe, 0x0, 0x0, 0x1, 0xdf, 0x50,
    0xfe, 0x0, 0x0, 0x0, 0x1e, 0xf3,

    /* U+004C "L" */
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xaa, 0xaa, 0xaa, 0xa0, 0xff, 0xff, 0xff, 0xff,
    0xf1,

    /* U+004D "M" */
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x2f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0xe, 0xff, 0x2f, 0xff,
    0xa0, 0x0, 0x0, 0x8, 0xfe, 0xf2, 0xfd, 0x9f,
    0x40, 0x0, 0x2, 0xfb, 0xbf, 0x2f, 0xd1, 0xed,
    0x0, 0x0, 0xbf, 0x2a, 0xf2, 0xfd, 0x6, 0xf7,
    0x0, 0x5f, 0x80, 0xaf, 0x2f, 0xd0, 0xc, 0xf2,
    0xe, 0xd0, 0xa, 0xf2, 0xfd, 0x0, 0x2f, 0xb8,
    0xf4, 0x0, 0xaf, 0x2f, 0xd0, 0x0, 0x8f, 0xfa,
    0x0, 0xa, 0xf2, 0xfd, 0x0, 0x0, 0xef, 0x10,
    0x0, 0xaf, 0x2f, 0xd0, 0x0, 0x3, 0x40, 0x0,
    0xa, 0xf2, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x20,

    /* U+004E "N" */
    0xfd, 0x10, 0x0, 0x0, 0x7, 0xf7, 0xff, 0xc0,
    0x0, 0x0, 0x7, 0xf7, 0xff, 0xf9, 0x0, 0x0,
    0x7, 0xf7, 0xfe, 0xdf, 0x60, 0x0, 0x7, 0xf7,
    0xfe, 0x2f, 0xf4, 0x0, 0x7, 0xf7, 0xfe, 0x5,
    0xfe, 0x20, 0x7, 0xf7, 0xfe, 0x0, 0x7f, 0xd0,
    0x7, 0xf7, 0xfe, 0x0, 0xa, 0xfb, 0x7, 0xf7,
    0xfe, 0x0, 0x0, 0xcf, 0x87, 0xf7, 0xfe, 0x0,
    0x0, 0x1e, 0xfc, 0xf7, 0xfe, 0x0, 0x0, 0x3,
    0xff, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0xfe, 0x0, 0x0, 0x0, 0x8, 0xf7,

    /* U+004F "O" */
    0x0, 0x0, 0x39, 0xdf, 0xfd, 0x92, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xdb, 0xbe, 0xff, 0x80, 0x0,
    0x0, 0x9f, 0xd4, 0x0, 0x0, 0x4d, 0xf9, 0x0,
    0x4, 0xfd, 0x10, 0x0, 0x0, 0x1, 0xef, 0x40,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0,
    0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0x4, 0xfd, 0x10, 0x0, 0x0, 0x1, 0xef, 0x40,
    0x0, 0x9f, 0xd4, 0x0, 0x0, 0x4d, 0xf9, 0x0,
    0x0, 0x8, 0xff, 0xeb, 0xbe, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x39, 0xdf, 0xfd, 0x92, 0x0, 0x0,

    /* U+0050 "P" */
    0xff, 0xff, 0xff, 0xea, 0x40, 0xf, 0xfa, 0xaa,
    0xac, 0xff, 0x80, 0xfe, 0x0, 0x0, 0x3, 0xef,
    0x4f, 0xe0, 0x0, 0x0, 0x6, 0xf9, 0xfe, 0x0,
    0x0, 0x0, 0x3f, 0xbf, 0xe0, 0x0, 0x0, 0x6,
    0xf9, 0xfe, 0x0, 0x0, 0x3, 0xef, 0x4f, 0xfa,
    0xaa, 0xac, 0xff, 0x80, 0xff, 0xff, 0xff, 0xea,
    0x40, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x29, 0xdf, 0xfd, 0x92, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xdb, 0xbe, 0xff, 0x80, 0x0,
    0x0, 0x9f, 0xd4, 0x0, 0x0, 0x4d, 0xf9, 0x0,
    0x4, 0xfd, 0x10, 0x0, 0x0, 0x1, 0xef, 0x40,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xe0,
    0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xb0,
    0x5, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x40,
    0x0, 0xbf, 0xc3, 0x0, 0x0, 0x3d, 0xfa, 0x0,
    0x0, 0xa, 0xff, 0xda, 0xad, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x4a, 0xef, 0xff, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xb4, 0x25, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x42, 0x0,

    /* U+0052 "R" */
    0xff, 0xff, 0xff, 0xea, 0x40, 0xf, 0xfa, 0xaa,
    0xac, 0xff, 0x80, 0xfe, 0x0, 0x0, 0x3, 0xef,
    0x4f, 0xe0, 0x0, 0x0, 0x6, 0xf9, 0xfe, 0x0,
    0x0, 0x0, 0x3f, 0xbf, 0xe0, 0x0, 0x0, 0x6,
    0xf9, 0xfe, 0x0, 0x0, 0x2, 0xef, 0x3f, 0xf9,
    0x99, 0x9c, 0xff, 0x80, 0xff, 0xff, 0xff, 0xff,
    0x30, 0xf, 0xe0, 0x0, 0x9, 0xf7, 0x0, 0xfe,
    0x0, 0x0, 0xd, 0xf3, 0xf, 0xe0, 0x0, 0x0,
    0x3f, 0xd0, 0xfe, 0x0, 0x0, 0x0, 0x7f, 0x90,

    /* U+0053 "S" */
    0x0, 0x18, 0xdf, 0xfe, 0xb5, 0x0, 0x2, 0xef,
    0xda, 0x9b, 0xff, 0x40, 0xa, 0xf7, 0x0, 0x0,
    0x16, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xc7, 0x30, 0x0, 0x0, 0x0, 0x2a, 0xff, 0xff,
    0xa2, 0x0, 0x0, 0x0, 0x3, 0x7c, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0xb, 0x60, 0x0, 0x0,
    0x5f, 0xc0, 0x1d, 0xff, 0xba, 0xac, 0xff, 0x30,
    0x0, 0x5a, 0xef, 0xfd, 0x91, 0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x9a, 0xaa,
    0xcf, 0xda, 0xaa, 0xa0, 0x0, 0x0, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,

    /* U+0055 "U" */
    0x2f, 0xc0, 0x0, 0x0, 0x0, 0xbf, 0x22, 0xfc,
    0x0, 0x0, 0x0, 0xb, 0xf2, 0x2f, 0xc0, 0x0,
    0x0, 0x0, 0xbf, 0x22, 0xfc, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x2f, 0xc0, 0x0, 0x0, 0x0, 0xbf,
    0x22, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xf2, 0x2f,
    0xc0, 0x0, 0x0, 0x0, 0xbf, 0x21, 0xfc, 0x0,
    0x0, 0x0, 0xb, 0xf1, 0xf, 0xe0, 0x0, 0x0,
    0x0, 0xdf, 0x0, 0xcf, 0x30, 0x0, 0x0, 0x2f,
    0xc0, 0x6, 0xfd, 0x20, 0x0, 0x2d, 0xf6, 0x0,
    0x9, 0xff, 0xcb, 0xcf, 0xf9, 0x0, 0x0, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0056 "V" */
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x50,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x7,
    0xf9, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x6f, 0x80, 0x0, 0x0, 0x8f,
    0x80, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0xa, 0xf6,
    0x0, 0xbf, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xd0,
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x4a,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe0, 0x0,
    0x0, 0x0,

    /* U+0057 "W" */
    0x3f, 0xb0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0,
    0x0, 0x3f, 0x90, 0xef, 0x10, 0x0, 0x0, 0xaf,
    0xf2, 0x0, 0x0, 0x8, 0xf4, 0x8, 0xf6, 0x0,
    0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0xee, 0x0,
    0x3f, 0xc0, 0x0, 0x5, 0xf7, 0xfd, 0x0, 0x0,
    0x3f, 0x90, 0x0, 0xdf, 0x10, 0x0, 0xbf, 0x1a,
    0xf3, 0x0, 0x9, 0xf3, 0x0, 0x8, 0xf6, 0x0,
    0x1f, 0xb0, 0x4f, 0x80, 0x0, 0xee, 0x0, 0x0,
    0x2f, 0xc0, 0x6, 0xf5, 0x0, 0xee, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0xdf, 0x10, 0xcf, 0x0, 0x9,
    0xf3, 0x9, 0xf3, 0x0, 0x0, 0x7, 0xf7, 0x1f,
    0xa0, 0x0, 0x3f, 0x90, 0xed, 0x0, 0x0, 0x0,
    0x2f, 0xc7, 0xf5, 0x0, 0x0, 0xee, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0xcf, 0xee, 0x0, 0x0, 0x8,
    0xfd, 0xf3, 0x0, 0x0, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0xdf, 0x80, 0x0,
    0x0,

    /* U+0058 "X" */
    0x2f, 0xf2, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x5f,
    0xc0, 0x0, 0x1, 0xef, 0x20, 0x0, 0x9f, 0x90,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xdf, 0x50, 0x7f,
    0x90, 0x0, 0x0, 0x2, 0xfe, 0x5f, 0xc0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfe, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xfb, 0x1e,
    0xf3, 0x0, 0x0, 0x2, 0xfe, 0x10, 0x4f, 0xd0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x8f, 0xa0, 0x0,
    0xaf, 0x80, 0x0, 0x0, 0xcf, 0x60, 0x6f, 0xc0,
    0x0, 0x0, 0x1, 0xef, 0x30,

    /* U+0059 "Y" */
    0xc, 0xf4, 0x0, 0x0, 0x0, 0xc, 0xf1, 0x2,
    0xfd, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x8f,
    0x80, 0x0, 0x1, 0xfc, 0x0, 0x0, 0xd, 0xf2,
    0x0, 0xb, 0xf3, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0xaf, 0x50, 0xee,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xe9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x10, 0x0, 0x0,

    /* U+005A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xa, 0xaa,
    0xaa, 0xaa, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xa0, 0x0, 0x0, 0x0, 0x6, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xe1, 0x0, 0x0, 0x0,
    0x1, 0xef, 0x30, 0x0, 0x0, 0x0, 0xd, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x1e, 0xfd, 0xaa, 0xaa, 0xaa, 0xa9,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+005B "[" */
    0xff, 0xff, 0xfe, 0x77, 0xfd, 0x0, 0xfd, 0x0,
    0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0,
    0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0,
    0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0, 0xfd, 0x0,
    0xfe, 0x77, 0xff, 0xff,

    /* U+005C "\\" */
    0x57, 0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0xa0, 0x0, 0x0, 0x0, 0xaf, 0x0,
    0x0, 0x0, 0x4, 0xf5, 0x0, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x0, 0x0, 0x9f, 0x10, 0x0, 0x0,
    0x3, 0xf7, 0x0, 0x0, 0x0, 0xd, 0xc0, 0x0,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x0, 0xc, 0xd0, 0x0, 0x0, 0x0,
    0x6f, 0x30, 0x0, 0x0, 0x1, 0xf9, 0x0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0x0, 0x5f, 0x40,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0xa,
    0xf0,

    /* U+005D "]" */
    0xaf, 0xff, 0x54, 0x7b, 0xf5, 0x0, 0x7f, 0x50,
    0x7, 0xf5, 0x0, 0x7f, 0x50, 0x7, 0xf5, 0x0,
    0x7f, 0x50, 0x7, 0xf5, 0x0, 0x7f, 0x50, 0x7,
    0xf5, 0x0, 0x7f, 0x50, 0x7, 0xf5, 0x0, 0x7f,
    0x50, 0x7, 0xf5, 0x0, 0x7f, 0x50, 0x7, 0xf5,
    0x47, 0xbf, 0x5a, 0xff, 0xf5,

    /* U+005E "^" */
    0x0, 0x6, 0xf8, 0x0, 0x0, 0x0, 0xde, 0xe0,
    0x0, 0x0, 0x4f, 0x3f, 0x50, 0x0, 0xb, 0xb0,
    0xac, 0x0, 0x1, 0xf5, 0x3, 0xf3, 0x0, 0x8e,
    0x0, 0xd, 0x90, 0xe, 0x80, 0x0, 0x6f, 0x15,
    0xf1, 0x0, 0x0, 0xf7,

    /* U+005F "_" */
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+0060 "`" */
    0x38, 0x60, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x4e,
    0x90,

    /* U+0061 "a" */
    0x0, 0x6c, 0xef, 0xea, 0x10, 0x8, 0xfd, 0xa9,
    0xcf, 0xe1, 0x1, 0x30, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x3, 0xfa, 0x0, 0x8e, 0xff, 0xff,
    0xfb, 0x9, 0xfa, 0x43, 0x35, 0xfb, 0xf, 0xd0,
    0x0, 0x1, 0xfb, 0xf, 0xe0, 0x0, 0x8, 0xfb,
    0x9, 0xfb, 0x55, 0xaf, 0xfb, 0x0, 0x7d, 0xfe,
    0xb3, 0xfb,

    /* U+0062 "b" */
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x84, 0xbf, 0xfc, 0x60, 0x0, 0x4f, 0xdf,
    0xda, 0xae, 0xfb, 0x0, 0x4f, 0xf9, 0x0, 0x0,
    0xbf, 0x80, 0x4f, 0xd0, 0x0, 0x0, 0x1f, 0xe0,
    0x4f, 0x90, 0x0, 0x0, 0xb, 0xf1, 0x4f, 0x90,
    0x0, 0x0, 0xb, 0xf1, 0x4f, 0xd0, 0x0, 0x0,
    0x1f, 0xe0, 0x4f, 0xf8, 0x0, 0x0, 0xbf, 0x80,
    0x4f, 0xcf, 0xd9, 0xae, 0xfb, 0x0, 0x4f, 0x74,
    0xbf, 0xfc, 0x60, 0x0,

    /* U+0063 "c" */
    0x0, 0x6, 0xce, 0xfc, 0x70, 0x0, 0xb, 0xfe,
    0xa9, 0xdf, 0xb0, 0x8, 0xfa, 0x0, 0x0, 0x98,
    0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0,
    0x0, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xa0, 0x0, 0x9, 0x80, 0x0, 0xbf, 0xea, 0x9d,
    0xfb, 0x0, 0x0, 0x6c, 0xef, 0xd7, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x7, 0xcf, 0xfb, 0x39, 0xf3, 0x0, 0xcf,
    0xea, 0xae, 0xfd, 0xf3, 0x8, 0xfa, 0x0, 0x0,
    0x9f, 0xf3, 0xf, 0xe0, 0x0, 0x0, 0xe, 0xf3,
    0x2f, 0xb0, 0x0, 0x0, 0xa, 0xf3, 0x2f, 0xb0,
    0x0, 0x0, 0xa, 0xf3, 0xf, 0xe0, 0x0, 0x0,
    0xd, 0xf3, 0x9, 0xf9, 0x0, 0x0, 0x8f, 0xf3,
    0x0, 0xcf, 0xd8, 0x8c, 0xfd, 0xf3, 0x0, 0x7,
    0xcf, 0xfb, 0x48, 0xf3,

    /* U+0065 "e" */
    0x0, 0x7, 0xcf, 0xeb, 0x40, 0x0, 0xc, 0xfc,
    0x99, 0xef, 0x80, 0x8, 0xf6, 0x0, 0x0, 0xaf,
    0x40, 0xfc, 0x0, 0x0, 0x2, 0xfa, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xc2, 0xfc, 0x33, 0x33, 0x33,
    0x33, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xb1, 0x0, 0x4, 0x70, 0x0, 0xbf, 0xea, 0x9c,
    0xfe, 0x0, 0x0, 0x6c, 0xef, 0xd8, 0x10,

    /* U+0066 "f" */
    0x0, 0x8, 0xef, 0xc2, 0x0, 0x8f, 0xb8, 0xb0,
    0x0, 0xee, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xb0, 0x57, 0xfe, 0x77, 0x50,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x6, 0xcf, 0xfb, 0x45, 0xf5, 0x0, 0xcf,
    0xea, 0xad, 0xfc, 0xf5, 0x8, 0xfa, 0x0, 0x0,
    0x8f, 0xf5, 0xf, 0xe0, 0x0, 0x0, 0xc, 0xf5,
    0x2f, 0xb0, 0x0, 0x0, 0x8, 0xf5, 0x2f, 0xb0,
    0x0, 0x0, 0x7, 0xf5, 0xf, 0xe0, 0x0, 0x0,
    0xc, 0xf5, 0x8, 0xfa, 0x0, 0x0, 0x7f, 0xf5,
    0x0, 0xcf, 0xea, 0x9d, 0xfd, 0xf5, 0x0, 0x7,
    0xcf, 0xfb, 0x48, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x1, 0x81, 0x0, 0x0, 0x4f, 0xd0,
    0x6, 0xff, 0xc9, 0x9c, 0xff, 0x30, 0x0, 0x3a,
    0xdf, 0xfe, 0xa2, 0x0,

    /* U+0068 "h" */
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x84,
    0xbf, 0xfc, 0x50, 0x4, 0xfd, 0xfd, 0xab, 0xff,
    0x70, 0x4f, 0xf7, 0x0, 0x3, 0xff, 0x4, 0xfd,
    0x0, 0x0, 0xa, 0xf3, 0x4f, 0x90, 0x0, 0x0,
    0x8f, 0x44, 0xf8, 0x0, 0x0, 0x8, 0xf4, 0x4f,
    0x80, 0x0, 0x0, 0x8f, 0x44, 0xf8, 0x0, 0x0,
    0x8, 0xf4, 0x4f, 0x80, 0x0, 0x0, 0x8f, 0x44,
    0xf8, 0x0, 0x0, 0x8, 0xf4,

    /* U+0069 "i" */
    0x4f, 0x97, 0xfc, 0x4, 0x10, 0x0, 0x4f, 0x84,
    0xf8, 0x4f, 0x84, 0xf8, 0x4f, 0x84, 0xf8, 0x4f,
    0x84, 0xf8, 0x4f, 0x84, 0xf8,

    /* U+006A "j" */
    0x0, 0x3, 0xea, 0x0, 0x6, 0xfd, 0x0, 0x0,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfa, 0x0,
    0x2, 0xfa, 0x0, 0x2, 0xfa, 0x0, 0x2, 0xfa,
    0x0, 0x2, 0xfa, 0x0, 0x2, 0xfa, 0x0, 0x2,
    0xfa, 0x0, 0x2, 0xfa, 0x0, 0x2, 0xfa, 0x0,
    0x2, 0xfa, 0x0, 0x2, 0xfa, 0x0, 0x5, 0xf8,
    0x5a, 0x9e, 0xf2, 0x6e, 0xfd, 0x40,

    /* U+006B "k" */
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x4, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x8, 0xfb, 0x4, 0xf8, 0x0, 0x9, 0xfa,
    0x0, 0x4f, 0x80, 0xa, 0xfa, 0x0, 0x4, 0xf8,
    0xc, 0xfa, 0x0, 0x0, 0x4f, 0xad, 0xff, 0x50,
    0x0, 0x4, 0xff, 0xfa, 0xff, 0x20, 0x0, 0x4f,
    0xf6, 0x6, 0xfd, 0x0, 0x4, 0xf9, 0x0, 0x9,
    0xfa, 0x0, 0x4f, 0x80, 0x0, 0xc, 0xf7, 0x4,
    0xf8, 0x0, 0x0, 0x1e, 0xf4,

    /* U+006C "l" */
    0x4f, 0x84, 0xf8, 0x4f, 0x84, 0xf8, 0x4f, 0x84,
    0xf8, 0x4f, 0x84, 0xf8, 0x4f, 0x84, 0xf8, 0x4f,
    0x84, 0xf8, 0x4f, 0x84, 0xf8,

    /* U+006D "m" */
    0x4f, 0x75, 0xcf, 0xfb, 0x30, 0x3a, 0xef, 0xd7,
    0x0, 0x4f, 0xef, 0xb8, 0xbf, 0xf8, 0xfe, 0x99,
    0xef, 0xa0, 0x4f, 0xf5, 0x0, 0x6, 0xff, 0xc0,
    0x0, 0xe, 0xf2, 0x4f, 0xc0, 0x0, 0x0, 0xff,
    0x30, 0x0, 0x8, 0xf6, 0x4f, 0x90, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x6, 0xf7, 0x4f, 0x80, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x6, 0xf7, 0x4f, 0x80,
    0x0, 0x0, 0xdf, 0x0, 0x0, 0x6, 0xf7, 0x4f,
    0x80, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x6, 0xf7,
    0x4f, 0x80, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x6,
    0xf7, 0x4f, 0x80, 0x0, 0x0, 0xdf, 0x0, 0x0,
    0x6, 0xf7,

    /* U+006E "n" */
    0x4f, 0x75, 0xcf, 0xfc, 0x50, 0x4, 0xfd, 0xfc,
    0x9a, 0xff, 0x70, 0x4f, 0xf6, 0x0, 0x2, 0xff,
    0x4, 0xfc, 0x0, 0x0, 0xa, 0xf3, 0x4f, 0x90,
    0x0, 0x0, 0x8f, 0x44, 0xf8, 0x0, 0x0, 0x8,
    0xf4, 0x4f, 0x80, 0x0, 0x0, 0x8f, 0x44, 0xf8,
    0x0, 0x0, 0x8, 0xf4, 0x4f, 0x80, 0x0, 0x0,
    0x8f, 0x44, 0xf8, 0x0, 0x0, 0x8, 0xf4,

    /* U+006F "o" */
    0x0, 0x6, 0xce, 0xfc, 0x60, 0x0, 0x0, 0xbf,
    0xea, 0xae, 0xfc, 0x0, 0x8, 0xfa, 0x0, 0x0,
    0x9f, 0x90, 0xe, 0xe0, 0x0, 0x0, 0xe, 0xf0,
    0x2f, 0xb0, 0x0, 0x0, 0xa, 0xf3, 0x2f, 0xb0,
    0x0, 0x0, 0xa, 0xf3, 0xe, 0xe0, 0x0, 0x0,
    0xe, 0xf0, 0x8, 0xfa, 0x0, 0x0, 0x9f, 0x90,
    0x0, 0xbf, 0xea, 0x9d, 0xfc, 0x0, 0x0, 0x6,
    0xcf, 0xfc, 0x60, 0x0,

    /* U+0070 "p" */
    0x4f, 0x74, 0xbf, 0xfc, 0x60, 0x0, 0x4f, 0xdf,
    0xc8, 0x8d, 0xfb, 0x0, 0x4f, 0xf8, 0x0, 0x0,
    0xaf, 0x80, 0x4f, 0xd0, 0x0, 0x0, 0xf, 0xe0,
    0x4f, 0x90, 0x0, 0x0, 0xb, 0xf1, 0x4f, 0x90,
    0x0, 0x0, 0xc, 0xf1, 0x4f, 0xd0, 0x0, 0x0,
    0x1f, 0xe0, 0x4f, 0xf9, 0x0, 0x0, 0xbf, 0x80,
    0x4f, 0xdf, 0xd9, 0xae, 0xfb, 0x0, 0x4f, 0x84,
    0xbf, 0xfc, 0x60, 0x0, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7, 0xcf, 0xfb, 0x38, 0xf3, 0x0, 0xcf,
    0xea, 0xae, 0xfc, 0xf3, 0x8, 0xfa, 0x0, 0x0,
    0xaf, 0xf3, 0xf, 0xe0, 0x0, 0x0, 0xe, 0xf3,
    0x2f, 0xb0, 0x0, 0x0, 0xa, 0xf3, 0x2f, 0xb0,
    0x0, 0x0, 0xa, 0xf3, 0xf, 0xe0, 0x0, 0x0,
    0xe, 0xf3, 0x9, 0xfa, 0x0, 0x0, 0x9f, 0xf3,
    0x0, 0xcf, 0xea, 0x9d, 0xfd, 0xf3, 0x0, 0x7,
    0xcf, 0xfb, 0x39, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf3,

    /* U+0072 "r" */
    0x4f, 0x74, 0xcf, 0x24, 0xfc, 0xfe, 0xc1, 0x4f,
    0xf9, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x4, 0xf8, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x4,
    0xf8, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x7d, 0xfe, 0xc9, 0x20, 0xbf, 0xc9, 0x9c,
    0xf4, 0x2f, 0xb0, 0x0, 0x2, 0x2, 0xfd, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xc9, 0x61, 0x0, 0x4,
    0x9c, 0xef, 0xf5, 0x0, 0x0, 0x0, 0x4f, 0xd0,
    0x50, 0x0, 0x0, 0xfe, 0x5f, 0xea, 0x99, 0xef,
    0x70, 0x6b, 0xef, 0xeb, 0x50,

    /* U+0074 "t" */
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xb0, 0x57, 0xfe, 0x77, 0x50,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0xfd, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x0, 0x9f, 0xc8, 0xc1, 0x0, 0x9, 0xef, 0xb2,

    /* U+0075 "u" */
    0x5f, 0x70, 0x0, 0x0, 0xbf, 0x15, 0xf7, 0x0,
    0x0, 0xb, 0xf1, 0x5f, 0x70, 0x0, 0x0, 0xbf,
    0x15, 0xf7, 0x0, 0x0, 0xb, 0xf1, 0x5f, 0x70,
    0x0, 0x0, 0xbf, 0x15, 0xf7, 0x0, 0x0, 0xc,
    0xf1, 0x4f, 0x90, 0x0, 0x0, 0xef, 0x11, 0xfe,
    0x10, 0x0, 0x8f, 0xf1, 0x8, 0xfe, 0x98, 0xcf,
    0xef, 0x10, 0x6, 0xcf, 0xfb, 0x49, 0xf1,

    /* U+0076 "v" */
    0xc, 0xf1, 0x0, 0x0, 0x5, 0xf6, 0x5, 0xf8,
    0x0, 0x0, 0xc, 0xe0, 0x0, 0xee, 0x0, 0x0,
    0x3f, 0x80, 0x0, 0x7f, 0x50, 0x0, 0xbf, 0x10,
    0x0, 0x1f, 0xc0, 0x2, 0xfa, 0x0, 0x0, 0x9,
    0xf3, 0x9, 0xf3, 0x0, 0x0, 0x2, 0xfa, 0x1f,
    0xc0, 0x0, 0x0, 0x0, 0xbf, 0x9f, 0x50, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x0, 0x0,

    /* U+0077 "w" */
    0xbf, 0x0, 0x0, 0x6, 0xf8, 0x0, 0x0, 0xd,
    0xc5, 0xf6, 0x0, 0x0, 0xcf, 0xe0, 0x0, 0x3,
    0xf6, 0xe, 0xc0, 0x0, 0x2f, 0xdf, 0x40, 0x0,
    0x9f, 0x0, 0x9f, 0x20, 0x8, 0xf3, 0xfa, 0x0,
    0xf, 0xa0, 0x3, 0xf7, 0x0, 0xeb, 0xa, 0xf0,
    0x5, 0xf4, 0x0, 0xd, 0xd0, 0x4f, 0x50, 0x4f,
    0x60, 0xbe, 0x0, 0x0, 0x7f, 0x3a, 0xe0, 0x0,
    0xec, 0x1f, 0x80, 0x0, 0x1, 0xfa, 0xf9, 0x0,
    0x8, 0xfa, 0xf2, 0x0, 0x0, 0xb, 0xff, 0x30,
    0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xc0,
    0x0, 0x0, 0xbf, 0x60, 0x0,

    /* U+0078 "x" */
    0x3f, 0xd0, 0x0, 0x5, 0xfa, 0x0, 0x6f, 0xa0,
    0x2, 0xfd, 0x0, 0x0, 0x9f, 0x60, 0xde, 0x20,
    0x0, 0x0, 0xcf, 0xbf, 0x40, 0x0, 0x0, 0x2,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x4f, 0xfb, 0x0,
    0x0, 0x0, 0x1e, 0xe9, 0xf7, 0x0, 0x0, 0xc,
    0xf3, 0xb, 0xf4, 0x0, 0x9, 0xf7, 0x0, 0x1e,
    0xe1, 0x5, 0xfa, 0x0, 0x0, 0x3f, 0xc0,

    /* U+0079 "y" */
    0xd, 0xf1, 0x0, 0x0, 0x5, 0xf6, 0x6, 0xf8,
    0x0, 0x0, 0xc, 0xe0, 0x0, 0xee, 0x0, 0x0,
    0x3f, 0x80, 0x0, 0x8f, 0x50, 0x0, 0xaf, 0x10,
    0x0, 0x1f, 0xc0, 0x1, 0xfa, 0x0, 0x0, 0xa,
    0xf3, 0x7, 0xf3, 0x0, 0x0, 0x3, 0xfa, 0xe,
    0xc0, 0x0, 0x0, 0x0, 0xcf, 0x7f, 0x50, 0x0,
    0x0, 0x0, 0x5f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf1,
    0x0, 0x0, 0x1, 0x0, 0x4f, 0x90, 0x0, 0x0,
    0x1f, 0xa9, 0xfe, 0x10, 0x0, 0x0, 0x19, 0xef,
    0xb2, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x2f, 0xff, 0xff, 0xff, 0xf0, 0x7, 0x77, 0x77,
    0xbf, 0xc0, 0x0, 0x0, 0x2, 0xfe, 0x10, 0x0,
    0x0, 0x1d, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0x50,
    0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xb0, 0x0, 0x0, 0x3, 0xfd, 0x10, 0x0, 0x0,
    0x1e, 0xfa, 0x77, 0x77, 0x71, 0x3f, 0xff, 0xff,
    0xff, 0xf3,

    /* U+007B "{" */
    0x0, 0x4d, 0xf5, 0x1, 0xff, 0x82, 0x5, 0xf8,
    0x0, 0x6, 0xf6, 0x0, 0x6, 0xf6, 0x0, 0x6,
    0xf6, 0x0, 0x6, 0xf6, 0x0, 0x6, 0xf6, 0x0,
    0x2a, 0xf4, 0x0, 0xef, 0xc0, 0x0, 0x5c, 0xf4,
    0x0, 0x6, 0xf6, 0x0, 0x6, 0xf6, 0x0, 0x6,
    0xf6, 0x0, 0x6, 0xf6, 0x0, 0x5, 0xf7, 0x0,
    0x2, 0xff, 0x82, 0x0, 0x6d, 0xf5,

    /* U+007C "|" */
    0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb,
    0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb, 0xfb,
    0xfb, 0xfb,

    /* U+007D "}" */
    0xaf, 0xb2, 0x0, 0x4a, 0xfb, 0x0, 0x0, 0xdf,
    0x0, 0x0, 0xbf, 0x10, 0x0, 0xbf, 0x10, 0x0,
    0xbf, 0x10, 0x0, 0xbf, 0x10, 0x0, 0xbf, 0x10,
    0x0, 0xaf, 0x51, 0x0, 0x3f, 0xf9, 0x0, 0x9f,
    0x92, 0x0, 0xbf, 0x10, 0x0, 0xbf, 0x10, 0x0,
    0xbf, 0x10, 0x0, 0xbf, 0x10, 0x0, 0xcf, 0x0,
    0x5a, 0xfd, 0x0, 0xaf, 0xc2, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x11, 0xcf, 0xe4, 0x0,
    0x5d, 0x8e, 0x5a, 0xf6, 0x1b, 0xac, 0x60, 0x6,
    0xff, 0xe2, 0x31, 0x0, 0x0, 0x20, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7c, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x9e, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x26,
    0xbf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x38,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xaa,
    0xff, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfd, 0x83,
    0x0, 0x6f, 0xf0, 0x0, 0x4, 0xff, 0xfb, 0x61,
    0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x4f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x4,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0,
    0x18, 0xde, 0xef, 0xf0, 0x0, 0x4, 0xff, 0x20,
    0x0, 0xd, 0xff, 0xff, 0xff, 0x2, 0x78, 0x9f,
    0xf2, 0x0, 0x1, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xb, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x5, 0xab,
    0xa4, 0xc, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x20, 0x0, 0x44, 0x44, 0x44, 0x44, 0x44, 0x42,
    0x0, 0x2e, 0x41, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x4f, 0xff, 0xff, 0xfd, 0x88, 0x88,
    0x88, 0x8a, 0xff, 0xff, 0xff, 0x52, 0x6f, 0x80,
    0x0, 0x0, 0x0, 0x2f, 0xb2, 0x5f, 0xf3, 0x3,
    0xf8, 0x0, 0x0, 0x0, 0x2, 0xfa, 0x3, 0xff,
    0xa8, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x2f, 0xd8,
    0xaf, 0xfc, 0xbc, 0xf9, 0x0, 0x0, 0x0, 0x3,
    0xfe, 0xbc, 0xff, 0x30, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x3f, 0xf3, 0x4, 0xfe, 0xcc,
    0xcc, 0xcc, 0xcd, 0xfa, 0x3, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xf7,
    0x47, 0xf8, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x46,
    0xff, 0x30, 0x3f, 0x80, 0x0, 0x0, 0x0, 0x2f,
    0xa0, 0x3f, 0xf8, 0x69, 0xf8, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x68, 0xff, 0xed, 0xef, 0xfc, 0xcc,
    0xcc, 0xcc, 0xdf, 0xfd, 0xef, 0xc3, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x3, 0xc0,

    /* U+F00B "" */
    0x7a, 0xaa, 0xa2, 0x9, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0x7f, 0xff, 0xff, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x84,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xf6, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x11, 0x11, 0x0, 0x1, 0x11, 0x11, 0x11, 0x11,
    0x10, 0x8b, 0xbb, 0xb3, 0xa, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0x8f, 0xff, 0xff, 0x84, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xf6, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8b, 0xbb, 0xb3, 0xa, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x8f, 0xff, 0xff, 0x84, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xf6, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfd, 0x10,
    0x26, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfd,
    0x10, 0x2e, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfd, 0x10, 0xe, 0xff, 0xfa, 0x0, 0x0, 0x6f,
    0xff, 0xfd, 0x10, 0x0, 0xaf, 0xff, 0xfa, 0x0,
    0x6f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xbf, 0xff,
    0xfa, 0x6f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xad, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x1b, 0xb1, 0x0, 0x0, 0x0, 0xab, 0x10, 0xcf,
    0xfc, 0x0, 0x0, 0xb, 0xff, 0xd0, 0xef, 0xff,
    0xc0, 0x0, 0xbf, 0xff, 0xe0, 0x3e, 0xff, 0xfc,
    0x1b, 0xff, 0xff, 0x30, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xc,
    0xff, 0xff, 0x6e, 0xff, 0xfc, 0x0, 0xbf, 0xff,
    0xf3, 0x3, 0xef, 0xff, 0xc0, 0xef, 0xff, 0x30,
    0x0, 0x3e, 0xff, 0xf0, 0x4f, 0xf3, 0x0, 0x0,
    0x3, 0xef, 0x40, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x11, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x7a, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xb3, 0x0, 0xff,
    0xf0, 0x4, 0xb1, 0x0, 0x0, 0x3, 0xff, 0xd0,
    0xf, 0xff, 0x0, 0xef, 0xe2, 0x0, 0x1, 0xef,
    0xfe, 0x0, 0xff, 0xf0, 0x1e, 0xff, 0xd0, 0x0,
    0x9f, 0xfe, 0x20, 0xf, 0xff, 0x0, 0x3e, 0xff,
    0x80, 0x1f, 0xff, 0x40, 0x0, 0xff, 0xf0, 0x0,
    0x5f, 0xff, 0x6, 0xff, 0xb0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0xcf, 0xf5, 0x9f, 0xf6, 0x0, 0x0,
    0xff, 0xf0, 0x0, 0x7, 0xff, 0x8b, 0xff, 0x40,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x5f, 0xfa, 0xbf,
    0xf4, 0x0, 0x0, 0xdf, 0xc0, 0x0, 0x5, 0xff,
    0x99, 0xff, 0x70, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x8f, 0xf8, 0x5f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x40, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xe0, 0x7, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0xb,
    0xff, 0xfa, 0x30, 0x0, 0x3b, 0xff, 0xfb, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9d, 0xff,
    0xfd, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x1, 0x34, 0x31, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30, 0x29,
    0xff, 0xff, 0xf7, 0x0, 0x61, 0x0, 0x1, 0xef,
    0xbf, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xb0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x4, 0xff, 0xff, 0xff, 0xa1, 0x3,
    0xdf, 0xff, 0xff, 0xd1, 0x1, 0xdf, 0xff, 0xc0,
    0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0xb, 0xff,
    0xf7, 0x0, 0x0, 0xb, 0xff, 0xf7, 0x0, 0x0,
    0xbf, 0xff, 0x80, 0x0, 0x0, 0xcf, 0xff, 0x60,
    0x0, 0x5e, 0xff, 0xfe, 0x0, 0x0, 0x3f, 0xff,
    0xfc, 0x20, 0x5f, 0xff, 0xff, 0xfd, 0x53, 0x7f,
    0xff, 0xff, 0xff, 0x11, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xd,
    0xe7, 0xdf, 0xff, 0xff, 0xff, 0xa9, 0xf9, 0x0,
    0x0, 0x11, 0x0, 0x6f, 0xff, 0xfe, 0x30, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x85, 0x0, 0x7,
    0x98, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x90, 0xd, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xfb, 0x1d, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x3a, 0xff,
    0xde, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xb1, 0x30, 0x7f, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x3, 0xef, 0xf9, 0x8, 0xfc, 0x14, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x6f, 0xff, 0x60, 0xbf, 0xff,
    0xe3, 0x2d, 0xff, 0xa0, 0x0, 0x8, 0xff, 0xe4,
    0x2d, 0xff, 0xff, 0xff, 0x51, 0xbf, 0xfc, 0x10,
    0xbf, 0xfd, 0x24, 0xef, 0xff, 0xff, 0xff, 0xf8,
    0x9, 0xff, 0xe2, 0x9f, 0xb0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x6f, 0xe1, 0x6, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x3,
    0x20, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xf5, 0x44, 0xcf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xe0, 0x0, 0x8f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xe0, 0x0,
    0x8f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xe0, 0x0, 0x8f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xd0, 0x0, 0x7f, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x4, 0x88, 0x84, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0x66, 0xef, 0xff,
    0xe6, 0x66, 0x30, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x4, 0x77, 0x77, 0x74, 0x1c,
    0xfc, 0x14, 0x77, 0x77, 0x74, 0xff, 0xff, 0xff,
    0xf7, 0x17, 0x17, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x7b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1c, 0x66, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x20,

    /* U+F01C "" */
    0x0, 0x0, 0x3, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x2f, 0xfc, 0x22, 0x22, 0x22, 0x22,
    0x26, 0xff, 0x70, 0x0, 0x0, 0xbf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0, 0x6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xfc, 0x0, 0x2f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x70, 0xbf, 0xf4, 0x22,
    0x20, 0x0, 0x0, 0x0, 0x22, 0x22, 0xcf, 0xf2,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xff, 0xec, 0xcc, 0xcf, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x6b, 0xa0, 0x0, 0x0, 0x3a, 0xef, 0xff, 0xd8,
    0x20, 0x9, 0xff, 0x0, 0x1, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x8f, 0xf0, 0x2, 0xef, 0xff,
    0xda, 0x8a, 0xef, 0xff, 0xd9, 0xff, 0x0, 0xdf,
    0xfd, 0x40, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xf0,
    0x9f, 0xfc, 0x10, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0x1f, 0xfe, 0x10, 0x0, 0x0, 0x4f, 0xff,
    0xef, 0xff, 0xf6, 0xff, 0x60, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0x49, 0x91, 0x0, 0x0,
    0x0, 0x29, 0x99, 0x99, 0x99, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x2f, 0xf6, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x9, 0xff, 0x4f, 0xff, 0xfa, 0x99, 0xa2,
    0x0, 0x0, 0x3, 0xff, 0xe0, 0xff, 0xff, 0xd2,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xf6, 0xf, 0xff,
    0xff, 0xf7, 0x10, 0x0, 0x7, 0xff, 0xfb, 0x0,
    0xff, 0x9b, 0xff, 0xff, 0xca, 0xbf, 0xff, 0xfc,
    0x0, 0xf, 0xf9, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0x90, 0x1, 0x6c, 0xff,
    0xfd, 0x82, 0x0, 0x0, 0x4, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x61, 0x0, 0x0, 0x0,
    0xb, 0xf7, 0x0, 0x0, 0x0, 0xbf, 0xf8, 0x0,
    0x0, 0xb, 0xff, 0xf8, 0xbe, 0xee, 0xef, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xef, 0xff, 0xff, 0xff, 0xf8, 0x13,
    0x33, 0x4e, 0xff, 0xf8, 0x0, 0x0, 0x2, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x2e, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0xb3,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x51, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x80, 0x0, 0x0, 0xae, 0xee,
    0xef, 0xff, 0xf8, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x9e, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x6, 0xfd, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xa, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xbf, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x8f, 0xc0, 0xef, 0xff, 0xff, 0xff, 0xf8,
    0x7, 0xb1, 0x1, 0x44, 0x44, 0xef, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xb3, 0x0, 0x0,
    0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x61, 0x0, 0x0, 0x8, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0x7f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xf8, 0x0, 0x8, 0xe3, 0x8, 0xfa, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf8, 0x0, 0x5, 0xff, 0x30,
    0xcf, 0x30, 0xbe, 0xee, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x5f, 0xd0, 0x4f, 0xa0, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x9, 0xe3, 0xa, 0xf5, 0xe, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x5, 0xfd, 0x2,
    0xfa, 0xa, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xaf, 0x20, 0xfc, 0x9, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xcf, 0x20, 0xfb, 0x9,
    0xf3, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8, 0xfb,
    0x4, 0xf9, 0xb, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xf8, 0x7, 0xb1, 0xc, 0xf3, 0xf, 0xe0, 0x13,
    0x33, 0x4e, 0xff, 0xf8, 0x0, 0x0, 0x9f, 0xb0,
    0x6f, 0x90, 0x0, 0x0, 0x2, 0xef, 0xf8, 0x0,
    0x8, 0xfe, 0x10, 0xef, 0x20, 0x0, 0x0, 0x0,
    0x2e, 0xf7, 0x0, 0x6, 0xa1, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xb3, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x90, 0x0,
    0x0,

    /* U+F03E "" */
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x21, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0xe, 0xff, 0xff, 0xf7, 0x6f, 0xff,
    0xff, 0xff, 0xc2, 0x19, 0xff, 0xff, 0xf7, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xb3, 0xef,
    0xf7, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xb0,
    0x2, 0xe7, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xb0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0x96, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+F043 "" */
    0x0, 0x0, 0x1, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xff, 0xab, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0x75, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xcf, 0xb0, 0xdf, 0xff, 0xff, 0xff,
    0xd0, 0x6f, 0xf5, 0x19, 0xdf, 0xff, 0xff, 0x70,
    0xd, 0xff, 0x82, 0xf, 0xff, 0xfd, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x19,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x4,
    0x54, 0x10, 0x0, 0x0,

    /* U+F048 "" */
    0x49, 0x80, 0x0, 0x0, 0x0, 0x56, 0xa, 0xff,
    0x0, 0x0, 0x0, 0x8f, 0xf3, 0xaf, 0xf0, 0x0,
    0x0, 0xaf, 0xff, 0x4a, 0xff, 0x0, 0x0, 0xbf,
    0xff, 0xf4, 0xaf, 0xf0, 0x0, 0xcf, 0xff, 0xff,
    0x4a, 0xff, 0x1, 0xcf, 0xff, 0xff, 0xf4, 0xaf,
    0xf2, 0xdf, 0xff, 0xff, 0xff, 0x4a, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xf4, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xaf, 0xf9, 0xff, 0xff, 0xff, 0xff, 0x4a,
    0xff, 0x7, 0xff, 0xff, 0xff, 0xf4, 0xaf, 0xf0,
    0x5, 0xff, 0xff, 0xff, 0x4a, 0xff, 0x0, 0x4,
    0xff, 0xff, 0xf4, 0xaf, 0xf0, 0x0, 0x3, 0xef,
    0xff, 0x4a, 0xff, 0x0, 0x0, 0x2, 0xef, 0xf3,
    0x9f, 0xf0, 0x0, 0x0, 0x2, 0xde, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x28, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xfa, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x18, 0x99, 0x98, 0x20, 0x0, 0x38, 0x99, 0x97,
    0xc, 0xff, 0xff, 0xfe, 0x0, 0x1f, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xf1, 0x4, 0xff, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0x20, 0x4f, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0x4, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0x20, 0x4f,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2, 0x4,
    0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0x20,
    0x4f, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf2,
    0x4, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x20, 0x4f, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff,
    0xf2, 0x4, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0x20, 0x4f, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xf2, 0x4, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xff, 0x20, 0x4f, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xf2, 0x4, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0x10, 0x3f, 0xff, 0xff, 0xfc,
    0x6f, 0xff, 0xff, 0x80, 0x0, 0xaf, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04D "" */
    0x18, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x96,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F051 "" */
    0x28, 0x10, 0x0, 0x0, 0x4, 0x98, 0xb, 0xfe,
    0x20, 0x0, 0x0, 0x9f, 0xf1, 0xcf, 0xfe, 0x30,
    0x0, 0x9, 0xff, 0x1c, 0xff, 0xff, 0x40, 0x0,
    0x9f, 0xf1, 0xcf, 0xff, 0xff, 0x50, 0x9, 0xff,
    0x1c, 0xff, 0xff, 0xff, 0x60, 0x9f, 0xf1, 0xcf,
    0xff, 0xff, 0xff, 0x79, 0xff, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xf1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xcf, 0xff, 0xff, 0xff, 0xdb, 0xff, 0x1c,
    0xff, 0xff, 0xff, 0xc1, 0x9f, 0xf1, 0xcf, 0xff,
    0xff, 0xb0, 0x9, 0xff, 0x1c, 0xff, 0xff, 0xa0,
    0x0, 0x9f, 0xf1, 0xcf, 0xff, 0x90, 0x0, 0x9,
    0xff, 0x1c, 0xff, 0x80, 0x0, 0x0, 0x9f, 0xf1,
    0x8f, 0x70, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x59, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa2, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x1d, 0xf4, 0x0, 0x0, 0x1, 0xdf, 0xfc, 0x0,
    0x0, 0x1d, 0xff, 0xf4, 0x0, 0x1, 0xdf, 0xff,
    0x40, 0x0, 0x1d, 0xff, 0xf4, 0x0, 0x1, 0xdf,
    0xff, 0x40, 0x0, 0x1d, 0xff, 0xf4, 0x0, 0x0,
    0xdf, 0xff, 0x40, 0x0, 0x0, 0xaf, 0xff, 0x70,
    0x0, 0x0, 0xa, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x70, 0x0, 0x0, 0xa, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x70, 0x0, 0x0,
    0xa, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0x0, 0xa, 0xd1,

    /* U+F054 "" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0,
    0x0, 0x0, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xc0, 0x0, 0x0, 0x5, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x5,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xc0,
    0x0, 0x0, 0x6, 0xff, 0xfb, 0x0, 0x0, 0x9,
    0xff, 0xf8, 0x0, 0x0, 0x9f, 0xff, 0x90, 0x0,
    0x9, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff,
    0x90, 0x0, 0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0,
    0x2d, 0x80, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x6, 0x74, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x10, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x59, 0x99, 0x99,
    0xcf, 0xff, 0xa9, 0x99, 0x99, 0x10, 0x0, 0x0,
    0x7, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0, 0x0,
    0x0,

    /* U+F068 "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x95, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x91,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xef,
    0xff, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0xff, 0xfc, 0xab, 0xef, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfb, 0x20, 0x0, 0x7,
    0xff, 0xfd, 0x20, 0x0, 0x0, 0xbf, 0xff, 0xa0,
    0x1, 0xcb, 0x50, 0x4f, 0xff, 0xe3, 0x0, 0xa,
    0xff, 0xfe, 0x0, 0x0, 0xff, 0xf7, 0x9, 0xff,
    0xfe, 0x10, 0x5f, 0xff, 0xf8, 0x0, 0x4, 0xff,
    0xff, 0x2, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xf6,
    0xb, 0xcf, 0xff, 0xff, 0x30, 0xff, 0xff, 0xf4,
    0xdf, 0xff, 0xf6, 0xc, 0xff, 0xff, 0xff, 0x20,
    0xff, 0xff, 0xf3, 0x3f, 0xff, 0xf9, 0x6, 0xff,
    0xff, 0xfb, 0x3, 0xff, 0xff, 0xa0, 0x8, 0xff,
    0xff, 0x10, 0x7f, 0xff, 0xb1, 0xa, 0xff, 0xfd,
    0x0, 0x0, 0x9f, 0xff, 0xb0, 0x1, 0x32, 0x0,
    0x6f, 0xff, 0xd2, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0x40, 0x0, 0x19, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x3b, 0xff, 0xfe, 0xcd, 0xff, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xce, 0xff,
    0xda, 0x50, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x16, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xb1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfe, 0x30, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x3,
    0x8c, 0xff, 0xff, 0xd8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xef, 0xff, 0xda, 0xbe, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xd3, 0x0, 0x0, 0x5e, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf6, 0x9, 0x94, 0x2,
    0xef, 0xff, 0x50, 0x0, 0x0, 0x62, 0x0, 0x4e,
    0xff, 0xac, 0xff, 0x70, 0x6f, 0xff, 0xf3, 0x0,
    0x3, 0xff, 0x40, 0x2, 0xcf, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xfe, 0x0, 0xb, 0xff, 0xf8, 0x0,
    0x9, 0xff, 0xff, 0xf6, 0xd, 0xff, 0xff, 0x70,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x5f, 0xff, 0xf5,
    0xd, 0xff, 0xff, 0x60, 0x1, 0xff, 0xff, 0xd0,
    0x0, 0x2, 0xdf, 0xfc, 0x3f, 0xff, 0xfc, 0x0,
    0x0, 0x5f, 0xff, 0xf4, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x6, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xe5, 0x0, 0x0, 0x3,
    0xef, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xfd, 0xd5, 0x0, 0x1b, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x7b, 0xef, 0xfd, 0x40,
    0x0, 0x8f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf1, 0x0, 0xbf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x10, 0xb, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf2, 0x0, 0xcf, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x30, 0xd, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xf4, 0x0, 0xef, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x60,
    0xf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xbe, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x40, 0xd,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x70,
    0xa, 0xff, 0xff, 0xff, 0xff, 0x92, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x4, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x10,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0xf, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xed,
    0xee, 0xff, 0xff, 0x20, 0x7f, 0xff, 0xef, 0xff,
    0xf5, 0x0, 0x1, 0xdf, 0xd1, 0x6f, 0xff, 0x80,
    0xcf, 0xf5, 0x0, 0x0, 0x1, 0xc2, 0x5f, 0xff,
    0x90, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xa0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0x0, 0x2, 0x40, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xc0, 0x8b, 0x0, 0xbf, 0x60,
    0x0, 0x0, 0x2e, 0xff, 0xd1, 0x7f, 0xfa, 0xc,
    0xff, 0x60, 0xff, 0xff, 0xff, 0xe1, 0x6, 0xff,
    0xff, 0xff, 0xff, 0x6f, 0xff, 0xff, 0xe2, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0xde, 0xee, 0xe3,
    0x0, 0x0, 0x7, 0xee, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x20, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x91, 0xdf,
    0xff, 0x30, 0x0, 0x0, 0x9f, 0xff, 0x90, 0x1,
    0xdf, 0xff, 0x30, 0x0, 0x9f, 0xff, 0x90, 0x0,
    0x1, 0xdf, 0xff, 0x30, 0x8f, 0xff, 0x90, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0x29, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xf3, 0x9, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xb5, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x90, 0xbf, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x55, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x4f,
    0xff, 0xd1, 0x5, 0xff, 0xfc, 0x10, 0x0, 0x4f,
    0xff, 0xd1, 0x0, 0x5, 0xff, 0xfc, 0x10, 0x4f,
    0xff, 0xd1, 0x0, 0x0, 0x5, 0xff, 0xfc, 0x5f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xb1, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc, 0x10,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xc1, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x6, 0xff, 0xff, 0xfd,
    0x14, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xd1, 0x35, 0x55, 0x55,
    0x57, 0xff, 0x30, 0x0, 0xcf, 0xc7, 0xff, 0x5f,
    0xf4, 0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x27, 0x17, 0xff, 0x4, 0x60, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x30, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x30, 0x0,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x30, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x19, 0x43, 0xff, 0x34, 0x91,
    0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf6, 0xff, 0x6f, 0xf9, 0x0, 0x7, 0xff, 0x66,
    0x66, 0x66, 0x62, 0x2e, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x22,
    0xef, 0xff, 0xfe, 0x20, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x2e, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x3, 0x44, 0x44, 0x43, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xd4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x16, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x69, 0x99, 0xff, 0xff,
    0xe9, 0x99, 0x50, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x4, 0x77, 0x77, 0x60, 0xef,
    0xff, 0xd0, 0x67, 0x77, 0x74, 0xff, 0xff, 0xff,
    0x25, 0x99, 0x84, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x76, 0x66, 0x7e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1c, 0x66, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x20,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4, 0x0,
    0x0, 0x1, 0x7d, 0xd1, 0x0, 0x6, 0xff, 0xff,
    0x80, 0x0, 0x2, 0x9f, 0xff, 0xfb, 0x1, 0x9f,
    0xff, 0xfb, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xbe, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xfb,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x45,
    0x42, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x46, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xc1, 0x0, 0x0, 0x3, 0xac,
    0x80, 0x9f, 0xff, 0xff, 0xa0, 0x0, 0x4, 0xff,
    0xff, 0x7e, 0xfa, 0x8, 0xff, 0x0, 0x4, 0xff,
    0xff, 0xa0, 0xff, 0x70, 0x5f, 0xf1, 0x4, 0xff,
    0xff, 0xa0, 0xc, 0xfe, 0x9e, 0xff, 0x4, 0xff,
    0xff, 0xa0, 0x0, 0x3f, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x3a, 0xdf, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x4, 0xce, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xbe, 0xff, 0xfd, 0x10, 0x0, 0xcf, 0xe7,
    0xdf, 0xf0, 0x2e, 0xff, 0xfd, 0x10, 0xf, 0xf6,
    0x4, 0xff, 0x10, 0x2e, 0xff, 0xfd, 0x10, 0xef,
    0xc3, 0xbf, 0xf0, 0x0, 0x2e, 0xff, 0xfd, 0x17,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x2e, 0xff, 0xf7,
    0x8, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x17, 0x84,
    0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x8, 0x99, 0x99, 0x98, 0x7, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xe0, 0xfb,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xfe, 0xf,
    0xfb, 0x0, 0x11, 0x3, 0xff, 0xff, 0xff, 0xe0,
    0xef, 0xf8, 0xdf, 0xf9, 0x3f, 0xff, 0xff, 0xff,
    0x10, 0x0, 0xf, 0xff, 0x93, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xf9, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0x93, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xf9, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0x93, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xf9, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0x93,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xf9,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0x93, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xf9, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xb0, 0xab, 0xbb, 0xbb, 0xbb, 0xbb, 0xb4,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x3, 0x66, 0x66, 0x66, 0x66, 0x66, 0x20,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x5, 0x66, 0x66, 0x66, 0x66, 0x66, 0x30, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x60, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xaf, 0xf9, 0x55, 0x55, 0x55,
    0x55, 0x6f, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xd4, 0x16, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff,
    0xf2, 0x0, 0x8, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xf3, 0x0, 0x9, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xe6, 0x38, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x23, 0x33, 0x33, 0x33, 0x33, 0x33, 0x32,
    0x10,

    /* U+F0C9 "" */
    0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8a, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0E0 "" */
    0x6, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x76, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x9,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x6, 0xfd, 0x22, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x1a, 0xff, 0xff, 0x50, 0x9f, 0xff, 0xff,
    0xff, 0x90, 0x3e, 0xff, 0xff, 0xff, 0x90, 0x5e,
    0xff, 0xfe, 0x50, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x1b, 0xfa, 0x11, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe5, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x9c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,

    /* U+F0E7 "" */
    0x0, 0x5a, 0xaa, 0xaa, 0x90, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xfe, 0xbb, 0xbb,
    0x60, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x28, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x77, 0x7e, 0xff, 0xd7, 0x76, 0x30, 0x0,
    0x0, 0xff, 0xff, 0xf2, 0x4f, 0xff, 0xfd, 0x0,
    0x0, 0xf, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xa3, 0x22, 0x22,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xe0, 0x79, 0x99,
    0x98, 0x7, 0x0, 0xf, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xe0, 0xfb, 0x0, 0xff, 0xff, 0xc1, 0xff,
    0xff, 0xfe, 0xf, 0xfb, 0xf, 0xff, 0xfc, 0x1f,
    0xff, 0xff, 0xe0, 0xef, 0xf8, 0xff, 0xff, 0xc1,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xf, 0xff, 0xfc,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xc1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xfc, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9,
    0xcc, 0xc9, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x3, 0x55, 0x55, 0x55,
    0x55, 0x51,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xb1,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x89,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x10, 0x0,
    0x0, 0x0,

    /* U+F11C "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x43, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x82, 0x6f, 0x22, 0xf6, 0x24, 0xf3,
    0x27, 0xe2, 0x2f, 0xf6, 0xff, 0x60, 0x4f, 0x0,
    0xe4, 0x2, 0xf1, 0x5, 0xd0, 0xf, 0xf6, 0xff,
    0x94, 0x8f, 0x44, 0xf8, 0x46, 0xf6, 0x49, 0xe4,
    0x5f, 0xf6, 0xff, 0xff, 0xfe, 0xff, 0xef, 0xff,
    0xef, 0xfe, 0xef, 0xff, 0xf6, 0xff, 0xff, 0x20,
    0x89, 0x1, 0xf4, 0x4, 0xf0, 0xc, 0xff, 0xf6,
    0xff, 0xff, 0x20, 0x89, 0x1, 0xf4, 0x4, 0xf0,
    0xc, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0x93,
    0x7f, 0x43, 0x33, 0x33, 0x33, 0x38, 0xe3, 0x4f,
    0xf6, 0xff, 0x60, 0x4f, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xd0, 0xf, 0xf6, 0xff, 0xa6, 0x9f, 0x66,
    0x66, 0x66, 0x66, 0x6a, 0xe6, 0x6f, 0xf6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x8f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x1, 0x8e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x19, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x11, 0x11, 0x11,
    0xbf, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xad, 0xdd, 0xdd, 0xdd, 0x48, 0x40, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xf5, 0xaf, 0x40, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x5a, 0xff, 0x40, 0xf, 0xff,
    0xff, 0xff, 0xf5, 0xaf, 0xff, 0x40, 0xff, 0xff,
    0xff, 0xff, 0x59, 0xee, 0xed, 0xf, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xee, 0xee, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x58, 0x9a, 0xa9, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x50, 0x0,
    0x3, 0xdf, 0xff, 0xff, 0xb8, 0x65, 0x57, 0x9d,
    0xff, 0xff, 0xfb, 0x10, 0x5f, 0xff, 0xfd, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x17, 0xef, 0xff, 0xe2,
    0xdf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf9, 0x2e, 0xc2, 0x0, 0x0,
    0x37, 0x9a, 0xa9, 0x62, 0x0, 0x0, 0x4e, 0xc0,
    0x1, 0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff,
    0xb3, 0x0, 0x1, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xe9, 0x64, 0x46, 0xaf,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xf7,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x41, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0x71, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0xaf, 0xf9,
    0xff, 0x64, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xb0, 0xaf, 0xfe, 0xff, 0x67, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7d, 0xfe,
    0xff, 0x67, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x7, 0xfe, 0xff, 0x67, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7, 0xfe,
    0xff, 0x67, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8e, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0xff, 0xa6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0xcf, 0xd5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10,

    /* U+F241 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x41, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0x71, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0xaf, 0xf9,
    0xff, 0x62, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb7,
    0x0, 0x0, 0xaf, 0xfe, 0xff, 0x63, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x7d, 0xfe,
    0xff, 0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x7, 0xfe, 0xff, 0x63, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x7, 0xfe,
    0xff, 0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x8e, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0xff, 0xa6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0xcf, 0xd5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10,

    /* U+F242 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x41, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0x71, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0xaf, 0xf9,
    0xff, 0x64, 0xbb, 0xbb, 0xbb, 0xba, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xff, 0x66, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xfe,
    0xff, 0x66, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0xff, 0x66, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0xff, 0x66, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0xff, 0xa6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0xcf, 0xd5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10,

    /* U+F243 "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x41, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0x71, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0xaf, 0xf9,
    0xff, 0x67, 0xbb, 0xbb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xff, 0x6a, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xfe,
    0xff, 0x6a, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0xff, 0x6a, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0xff, 0x6a, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0xff, 0xa6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0xcf, 0xd5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10,

    /* U+F244 "" */
    0x0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0x95, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0xcf, 0xd6,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0xfe,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8e, 0xfe, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0xff, 0xa6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0xcf, 0xd5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x10,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcd, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xa8, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcb, 0x0, 0x3b, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x1a, 0xec, 0x30, 0x4,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0x70, 0x0,
    0xaf, 0xff, 0xe0, 0x1d, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfd, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xdf, 0xff, 0xf6, 0x55, 0x5a, 0xf7, 0x55, 0x55,
    0x55, 0x57, 0xff, 0xa1, 0x3e, 0xff, 0x70, 0x0,
    0x0, 0xe8, 0x0, 0x0, 0x0, 0x2, 0xd3, 0x0,
    0x1, 0x42, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x80, 0x6f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfa, 0xcf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5a, 0xdf, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x5a, 0xcd, 0xc9, 0x40, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xef, 0xff, 0xa0, 0x0, 0x1,
    0xef, 0xff, 0xe3, 0xff, 0xff, 0xa0, 0x0, 0x9f,
    0xff, 0xfe, 0x5, 0xff, 0xff, 0x30, 0xf, 0xff,
    0xff, 0xe0, 0x6, 0xff, 0xf9, 0x5, 0xff, 0xea,
    0xfe, 0x9, 0x8, 0xff, 0xd0, 0x8f, 0xf7, 0xa,
    0xe0, 0xe7, 0xd, 0xff, 0xa, 0xff, 0xf5, 0x8,
    0x9, 0x8, 0xff, 0xf2, 0xbf, 0xff, 0xf5, 0x0,
    0x7, 0xff, 0xff, 0x3b, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xf4, 0xbf, 0xff, 0xfd, 0x10, 0x1d,
    0xff, 0xff, 0x4a, 0xff, 0xfd, 0x11, 0x2, 0x1d,
    0xff, 0xf3, 0x9f, 0xfd, 0x12, 0xd0, 0xd3, 0x2e,
    0xff, 0x26, 0xff, 0x82, 0xef, 0xd, 0x41, 0xdf,
    0xf0, 0x3f, 0xff, 0xff, 0xf0, 0x30, 0xcf, 0xfc,
    0x0, 0xdf, 0xff, 0xff, 0x0, 0xcf, 0xff, 0x70,
    0x4, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xe0, 0x0,
    0x7, 0xff, 0xff, 0xbf, 0xff, 0xf4, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x21, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x4, 0x99, 0x99, 0x91, 0x0, 0x0,
    0x5, 0x66, 0x66, 0xef, 0xff, 0xff, 0xa6, 0x66,
    0x62, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0xdf, 0xf9, 0xef, 0xe9,
    0xff, 0xad, 0xff, 0x70, 0xd, 0xff, 0x2a, 0xfb,
    0x1f, 0xf4, 0x8f, 0xf7, 0x0, 0xdf, 0xf2, 0xaf,
    0xb1, 0xff, 0x48, 0xff, 0x70, 0xd, 0xff, 0x2a,
    0xfb, 0x1f, 0xf4, 0x8f, 0xf7, 0x0, 0xdf, 0xf2,
    0xaf, 0xb1, 0xff, 0x48, 0xff, 0x70, 0xd, 0xff,
    0x2a, 0xfb, 0x1f, 0xf4, 0x8f, 0xf7, 0x0, 0xdf,
    0xf2, 0xaf, 0xb1, 0xff, 0x48, 0xff, 0x70, 0xd,
    0xff, 0x2a, 0xfb, 0x1f, 0xf4, 0x8f, 0xf7, 0x0,
    0xdf, 0xf2, 0xaf, 0xb1, 0xff, 0x48, 0xff, 0x70,
    0xd, 0xff, 0x4b, 0xfc, 0x3f, 0xf6, 0xaf, 0xf7,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x4, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x52, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0x71, 0xdf,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xf7, 0x1d, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0x71, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf7, 0x17, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x53,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x3, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x43, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xee, 0xff, 0xff, 0xfb,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0x80, 0x7f, 0xfd,
    0x12, 0xef, 0xff, 0xfc, 0x3, 0xef, 0xff, 0xff,
    0xff, 0x40, 0x7, 0xd1, 0x0, 0xcf, 0xff, 0xfc,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfc, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0xaf, 0xff, 0xff, 0xfc,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x7f, 0xff, 0xff, 0xfc, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x20, 0x7, 0xff, 0xff, 0xfc,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x30, 0xa, 0xf3,
    0x0, 0xbf, 0xff, 0xfc, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xb1, 0xaf, 0xff, 0x44, 0xff, 0xff, 0xfc,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x36, 0x66, 0x66, 0x66, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x5f,
    0xf2, 0x1f, 0x21, 0xf1, 0x2f, 0xf4, 0x6f, 0xff,
    0x21, 0xf2, 0x1f, 0x12, 0xff, 0x4f, 0xff, 0xf2,
    0x1f, 0x21, 0xf1, 0x2f, 0xf4, 0xff, 0xff, 0xee,
    0xfe, 0xef, 0xee, 0xff, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x2,
    0x22, 0x22, 0x22, 0x22, 0x20, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xf1, 0x0, 0x0, 0x34, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x4,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf1, 0x6, 0xff, 0xff, 0x33, 0x33, 0x33,
    0x33, 0x33, 0xef, 0xf1, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x2e, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0x90, 0x1, 0xdf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 82, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 81, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 20, .adv_w = 119, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 38, .adv_w = 214, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 123, .adv_w = 189, .box_w = 12, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 231, .adv_w = 256, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 335, .adv_w = 209, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 426, .adv_w = 64, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 432, .adv_w = 102, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 477, .adv_w = 103, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 522, .adv_w = 122, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 550, .adv_w = 177, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 591, .adv_w = 69, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 603, .adv_w = 116, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 609, .adv_w = 69, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 615, .adv_w = 107, .box_w = 9, .box_h = 18, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 696, .adv_w = 203, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 774, .adv_w = 112, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 813, .adv_w = 174, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 885, .adv_w = 174, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 957, .adv_w = 203, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1042, .adv_w = 174, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1114, .adv_w = 188, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1192, .adv_w = 182, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1264, .adv_w = 196, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1342, .adv_w = 188, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1414, .adv_w = 69, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1434, .adv_w = 69, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1460, .adv_w = 177, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1501, .adv_w = 177, .box_w = 9, .box_h = 7, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 1533, .adv_w = 177, .box_w = 9, .box_h = 9, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1574, .adv_w = 174, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1639, .adv_w = 314, .box_w = 19, .box_h = 17, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1801, .adv_w = 223, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1899, .adv_w = 230, .box_w = 12, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1977, .adv_w = 220, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2068, .adv_w = 251, .box_w = 13, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2153, .adv_w = 204, .box_w = 10, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2218, .adv_w = 193, .box_w = 10, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2283, .adv_w = 235, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2374, .adv_w = 247, .box_w = 12, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2452, .adv_w = 94, .box_w = 2, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2465, .adv_w = 156, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2524, .adv_w = 219, .box_w = 12, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2602, .adv_w = 181, .box_w = 10, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2667, .adv_w = 290, .box_w = 15, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2765, .adv_w = 247, .box_w = 12, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2843, .adv_w = 255, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2947, .adv_w = 219, .box_w = 11, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3019, .adv_w = 255, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3147, .adv_w = 221, .box_w = 11, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3219, .adv_w = 189, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3297, .adv_w = 178, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3375, .adv_w = 240, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3460, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3558, .adv_w = 342, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3695, .adv_w = 205, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3780, .adv_w = 197, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3871, .adv_w = 200, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3949, .adv_w = 101, .box_w = 4, .box_h = 18, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 3985, .adv_w = 107, .box_w = 9, .box_h = 18, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4066, .adv_w = 101, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4111, .adv_w = 177, .box_w = 9, .box_h = 8, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4147, .adv_w = 152, .box_w = 10, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4157, .adv_w = 182, .box_w = 6, .box_h = 3, .ofs_x = 2, .ofs_y = 11},
    {.bitmap_index = 4166, .adv_w = 182, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4216, .adv_w = 207, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4300, .adv_w = 174, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4355, .adv_w = 207, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4439, .adv_w = 186, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4494, .adv_w = 107, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4550, .adv_w = 210, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4634, .adv_w = 207, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4711, .adv_w = 85, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4732, .adv_w = 86, .box_w = 6, .box_h = 18, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 4786, .adv_w = 187, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4863, .adv_w = 85, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4884, .adv_w = 321, .box_w = 18, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4974, .adv_w = 207, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5029, .adv_w = 193, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5089, .adv_w = 207, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5173, .adv_w = 207, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5257, .adv_w = 125, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5292, .adv_w = 152, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5337, .adv_w = 126, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5385, .adv_w = 206, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5440, .adv_w = 170, .box_w = 12, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5500, .adv_w = 273, .box_w = 17, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5585, .adv_w = 168, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5640, .adv_w = 170, .box_w = 12, .box_h = 14, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 5724, .adv_w = 158, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5774, .adv_w = 107, .box_w = 6, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5828, .adv_w = 91, .box_w = 2, .box_h = 18, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 5846, .adv_w = 107, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5900, .adv_w = 177, .box_w = 9, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 5923, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6113, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6256, .adv_w = 304, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6427, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6570, .adv_w = 209, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6668, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6858, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7048, .adv_w = 342, .box_w = 22, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7246, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7436, .adv_w = 342, .box_w = 22, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7601, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7791, .adv_w = 152, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7866, .adv_w = 228, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7979, .adv_w = 342, .box_w = 22, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8188, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8331, .adv_w = 209, .box_w = 14, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8471, .adv_w = 266, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8588, .adv_w = 266, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8758, .adv_w = 266, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8911, .adv_w = 266, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9064, .adv_w = 266, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 9181, .adv_w = 266, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9343, .adv_w = 190, .box_w = 10, .box_h = 17, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9428, .adv_w = 190, .box_w = 10, .box_h = 17, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9513, .adv_w = 266, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9666, .adv_w = 266, .box_w = 17, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 9700, .adv_w = 342, .box_w = 22, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9865, .adv_w = 380, .box_w = 24, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10105, .adv_w = 342, .box_w = 23, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10335, .adv_w = 304, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10506, .adv_w = 266, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 10600, .adv_w = 266, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 10694, .adv_w = 380, .box_w = 24, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10886, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11029, .adv_w = 304, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11219, .adv_w = 304, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11419, .adv_w = 266, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11572, .adv_w = 266, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11742, .adv_w = 266, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11895, .adv_w = 266, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12031, .adv_w = 304, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12174, .adv_w = 190, .box_w = 14, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12314, .adv_w = 266, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12484, .adv_w = 266, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12654, .adv_w = 342, .box_w = 22, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12819, .adv_w = 304, .box_w = 21, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13029, .adv_w = 228, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13179, .adv_w = 380, .box_w = 24, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13407, .adv_w = 380, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13563, .adv_w = 380, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13719, .adv_w = 380, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13875, .adv_w = 380, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14031, .adv_w = 380, .box_w = 24, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 14187, .adv_w = 380, .box_w = 24, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14379, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14529, .adv_w = 266, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14699, .adv_w = 304, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 14899, .adv_w = 380, .box_w = 24, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15079, .adv_w = 228, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15229, .adv_w = 306, .box_w = 20, .box_h = 13, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x42,
    0x47, 0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53,
    0x66, 0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77,
    0x78, 0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xc8,
    0xdf, 0xe6, 0xe9, 0xf2, 0x11b, 0x123, 0x15a, 0x1ea,
    0x23f, 0x240, 0x241, 0x242, 0x243, 0x286, 0x292, 0x2ec,
    0x303, 0x559, 0x7c1, 0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 60, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 14, 0, 8, -7, 0, 0, 0,
    0, -17, -18, 2, 14, 7, 5, -12,
    2, 15, 1, 13, 3, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 18, 2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -9, 0, 0, 0, 0, 0, -6,
    5, 6, 0, 0, -3, 0, -2, 3,
    0, -3, 0, -3, -2, -6, 0, 0,
    0, 0, -3, 0, 0, -4, -5, 0,
    0, -3, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -8, 0, -37, 0, 0, -6, 0,
    6, 9, 0, 0, -6, 3, 3, 10,
    6, -5, 6, 0, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, -15, 0, -12, -2, 0, 0, 0,
    0, 1, 12, 0, -9, -2, -1, 1,
    0, -5, 0, 0, -2, -22, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, -2, 12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 10, 0, 3, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 2, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 6, 3, 9, -3, 0, 0, 6,
    -3, -10, -42, 2, 8, 6, 1, -4,
    0, 11, 0, 10, 0, 10, 0, -28,
    0, -4, 9, 0, 10, -3, 6, 3,
    0, 0, 1, -3, 0, 0, -5, 24,
    0, 24, 0, 9, 0, 13, 4, 5,
    0, 0, 0, -11, 0, 0, 0, 0,
    1, -2, 0, 2, -5, -4, -6, 2,
    0, -3, 0, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -17, 0, -19, 0, 0, 0, 0,
    -2, 0, 30, -4, -4, 3, 3, -3,
    0, -4, 3, 0, 0, -16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -29, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 18, 0, 0, -11, 0, 10, 0,
    -21, -29, -21, -6, 9, 0, 0, -20,
    0, 4, -7, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 9, -37, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 2,
    2, -4, -6, 0, -1, -1, -3, 0,
    0, -2, 0, 0, 0, -6, 0, -2,
    0, -7, -6, 0, -8, -10, -10, -6,
    0, -6, 0, -6, 0, 0, 0, 0,
    -2, 0, 0, 3, 0, 2, -3, 0,
    0, 0, 0, 3, -2, 0, 0, 0,
    -2, 3, 3, -1, 0, 0, 0, -6,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 4, -2, 0, -4, 0, -5, 0,
    0, -2, 0, 9, 0, 0, -3, 0,
    0, 0, 0, 0, -1, 1, -2, -2,
    0, -3, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    -3, -4, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -2, -4, 0,
    0, -9, -2, -9, 6, 0, 0, -6,
    3, 6, 8, 0, -8, -1, -4, 0,
    -1, -14, 3, -2, 2, -16, 3, 0,
    0, 1, -16, 0, -16, -2, -26, -2,
    0, -15, 0, 6, 9, 0, 4, 0,
    0, 0, 0, 1, 0, -5, -4, 0,
    0, 0, 0, -3, 0, 0, 0, -3,
    0, 0, 0, 0, 0, -2, -2, 0,
    -2, -4, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, -2, -4, -2, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    0, -2, 0, -6, 3, 0, 0, -4,
    2, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -4, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    -2, 0, 0, 0, 0, -3, -5, 0,
    0, 9, -2, 1, -10, 0, 0, 8,
    -15, -16, -13, -6, 3, 0, -2, -20,
    -5, 0, -5, 0, -6, 5, -5, -19,
    0, -8, 0, 0, 2, -1, 2, -2,
    0, 3, 0, -9, -12, 0, -15, -7,
    -6, -7, -9, -4, -8, -1, -6, -8,
    0, 1, 0, -3, 0, 0, 0, 2,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, -2,
    0, -1, -3, 0, -5, -7, -7, -1,
    0, -9, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 1, -2, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 15, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -6, 0, 0, 0,
    0, -15, -9, 0, 0, 0, -5, -15,
    0, 0, -3, 3, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 4, 0,
    2, -6, -6, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -9, 0, -3,
    0, -5, -3, 0, -7, -8, -9, -2,
    0, -6, 0, -9, 0, 0, 0, 0,
    24, 0, 0, 2, 0, 0, -4, 0,
    0, -13, 0, 0, 0, 0, 0, -28,
    -5, 10, 9, -2, -13, 0, 3, -5,
    0, -15, -2, -4, 3, -21, -3, 4,
    0, 5, -11, -5, -11, -10, -13, 0,
    0, -18, 0, 17, 0, 0, -2, 0,
    0, 0, -2, -2, -3, -8, -10, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -2, -3, -5, 0,
    0, -6, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 6,
    -1, 4, 0, -7, 3, -2, -1, -8,
    -3, 0, -4, -3, -2, 0, -5, -5,
    0, 0, -2, -1, -2, -5, -4, 0,
    0, -3, 0, 3, -2, 0, -7, 0,
    0, 0, -6, 0, -5, 0, -5, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 3, 0, -4, 0, -2, -4, -9,
    -2, -2, -2, -1, -2, -4, -1, 0,
    0, 0, 0, 0, -3, -2, -2, 0,
    0, 0, 0, 4, -2, 0, -2, 0,
    0, 0, -2, -4, -2, -3, -4, -3,
    2, 12, -1, 0, -8, 0, -2, 6,
    0, -3, -13, -4, 5, 0, 0, -14,
    -5, 3, -5, 2, 0, -2, -2, -10,
    0, -5, 2, 0, 0, -5, 0, 0,
    0, 3, 3, -6, -6, 0, -5, -3,
    -5, -3, -3, 0, -5, 2, -6, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -4, 0, 0, -3, -3, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -2, 0,
    0, 0, -5, 0, -6, 0, 0, 0,
    -10, 0, 2, -7, 6, 1, -2, -14,
    0, 0, -7, -3, 0, -12, -8, -9,
    0, 0, -13, -3, -12, -12, -15, 0,
    -8, 0, 2, 20, -4, 0, -7, -3,
    -1, -3, -5, -8, -5, -11, -12, -7,
    0, 0, -2, 0, 1, 0, 0, -21,
    -3, 9, 7, -7, -11, 0, 1, -9,
    0, -15, -2, -3, 6, -28, -4, 1,
    0, 0, -20, -4, -16, -3, -22, 0,
    0, -21, 0, 18, 1, 0, -2, 0,
    0, 0, 0, -2, -2, -12, -2, 0,
    0, 0, 0, 0, -10, 0, -3, 0,
    -1, -9, -14, 0, 0, -2, -5, -9,
    -3, 0, -2, 0, 0, 0, 0, -14,
    -3, -10, -10, -2, -5, -8, -3, -5,
    0, -6, -3, -10, -5, 0, -4, -6,
    -3, -6, 0, 2, 0, -2, -10, 0,
    0, -5, 0, 0, 0, 0, 4, 0,
    2, -6, 12, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -9, 0, -3,
    0, -5, -3, 0, -7, -8, -9, -2,
    0, -6, 2, 12, 0, 0, 0, 0,
    24, 0, 0, 2, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, -6,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, -3, 0, 0, -6, -3, 0,
    0, -6, 0, 5, -2, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    6, 2, -3, 0, -10, -5, 0, 9,
    -10, -10, -6, -6, 12, 5, 3, -26,
    -2, 6, -3, 0, -3, 3, -3, -11,
    0, -3, 3, -4, -2, -9, -2, 0,
    0, 9, 6, 0, -9, 0, -17, -4,
    9, -4, -12, 1, -4, -10, -10, -3,
    3, 0, -5, 0, -8, 0, 2, 10,
    -7, -11, -12, -8, 9, 0, 1, -22,
    -2, 3, -5, -2, -7, 0, -7, -11,
    -5, -5, -2, 0, 0, -7, -6, -3,
    0, 9, 7, -3, -17, 0, -17, -4,
    0, -11, -18, -1, -10, -5, -10, -9,
    0, 0, -4, 0, -6, -3, 0, -3,
    -5, 0, 5, -10, 3, 0, 0, -16,
    0, -3, -7, -5, -2, -9, -8, -10,
    -7, 0, -9, -3, -7, -6, -9, -3,
    0, 0, 1, 14, -5, 0, -9, -3,
    0, -3, -6, -7, -8, -9, -12, -4,
    6, 0, -5, 0, -15, -4, 2, 6,
    -10, -11, -6, -10, 10, -3, 2, -28,
    -5, 6, -7, -5, -11, 0, -9, -13,
    -4, -3, -2, -3, -6, -9, -1, 0,
    0, 9, 9, -2, -20, 0, -18, -7,
    7, -12, -21, -6, -11, -13, -15, -10,
    0, 0, 0, 0, -4, 0, 0, 3,
    -4, 6, 2, -6, 6, 0, 0, -9,
    -1, 0, -1, 0, 1, 1, -2, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 2, 9, 1, 0, -4, 0,
    0, 0, 0, -2, -2, -4, 0, 0,
    1, 2, 0, 0, 0, 0, 2, 0,
    -2, 0, 12, 0, 5, 1, 1, -4,
    0, 6, 0, 0, 0, 2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 0, 9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -18, 0, -3, 5, 0, 9, 0,
    0, 30, 4, -6, -6, 3, 3, -2,
    1, -15, 0, 0, 15, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 12, 43, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, -6, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -8, 0, 0, 1, 0,
    0, 3, 39, -6, -2, 10, 8, -8,
    3, 0, 0, 3, 3, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -40, 9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, -8,
    0, 0, 0, 0, -7, -2, 0, 0,
    0, -7, 0, -4, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -5, 0, -8, 0, 0, 0, -5,
    3, -4, 0, 0, -8, -3, -7, 0,
    0, -8, 0, -3, 0, -14, 0, -3,
    0, 0, -25, -6, -12, -3, -11, 0,
    0, -20, 0, -8, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -5, -2,
    0, 0, 0, 0, -7, 0, -7, 4,
    -3, 6, 0, -2, -7, -2, -5, -6,
    0, -4, -2, -2, 2, -8, -1, 0,
    0, 0, -27, -2, -4, 0, -7, 0,
    -2, -14, -3, 0, 0, -2, -2, 0,
    0, 0, 0, 2, 0, -2, -5, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, -7, 0, -2, 0, 0, 0, -6,
    3, 0, 0, 0, -8, -3, -6, 0,
    0, -9, 0, -3, 0, -14, 0, 0,
    0, 0, -29, 0, -6, -11, -15, 0,
    0, -20, 0, -2, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -5, -2,
    1, 0, 0, 5, -4, 0, 9, 15,
    -3, -3, -9, 4, 15, 5, 7, -8,
    4, 13, 4, 9, 7, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 19, 14, -5, -3, 0, -2, 24,
    13, 24, 0, 0, 0, 3, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -26, -4, -2, -12, -15, 0,
    0, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -26, -4, -2, -12, -15, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -7, 3, 0, -3, 2, 5, 3, -9,
    0, -1, -2, 3, 0, 2, 0, 0,
    0, 0, -8, 0, -3, -2, -6, 0,
    -3, -12, 0, 19, -3, 0, -7, -2,
    0, -2, -5, 0, -3, -9, -6, -4,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -26, -4, -2, -12, -15, 0,
    0, -20, 0, 0, 0, 0, 0, 0,
    15, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -10, -4, -3, 9,
    -3, -3, -12, 1, -2, 1, -2, -8,
    1, 7, 1, 2, 1, 2, -7, -12,
    -4, 0, -12, -6, -8, -13, -12, 0,
    -5, -6, -4, -4, -2, -2, -4, -2,
    0, -2, -1, 5, 0, 5, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -3, -3, 0,
    0, -8, 0, -2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -18, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 0, -2, 0, 0, -5,
    -3, 3, 0, -5, -6, -2, 0, -9,
    -2, -7, -2, -4, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 10, 0, 0, -5, 0,
    0, 0, 0, -4, 0, -3, 0, 0,
    0, 0, -2, 0, -7, 0, 0, 13,
    -4, -10, -9, 2, 3, 3, -1, -9,
    2, 5, 2, 9, 2, 10, -2, -8,
    0, 0, -12, 0, 0, -9, -8, 0,
    0, -6, 0, -4, -5, 0, -5, 0,
    -5, 0, -2, 5, 0, -2, -9, -3,
    0, 0, -3, 0, -6, 0, 0, 4,
    -7, 0, 3, -3, 2, 0, 0, -10,
    0, -2, -1, 0, -3, 3, -2, 0,
    0, 0, -12, -4, -7, 0, -9, 0,
    0, -14, 0, 11, -3, 0, -5, 0,
    2, 0, -3, 0, -3, -9, 0, -3,
    0, 0, 0, 0, -2, 0, 0, 3,
    -4, 1, 0, 0, -4, -2, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 7, 0, 0, -2, 0,
    0, 0, 0, 1, 0, -3, -3, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserratMedium_19 = {
#else
lv_font_t lv_font_montserratMedium_19 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 19,          /*The maximum line height required by the font  default: (f.src.ascent - f.src.descent)*/
    .base_line = 3,                          /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_MONTSERRATMEDIUM_19*/

