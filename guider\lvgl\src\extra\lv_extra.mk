CSRCS += lv_flex.c
CSRCS += lv_grid.c
CSRCS += lv_barcode.c
CSRCS += code128.c
CSRCS += lv_bmp.c
CSRCS += lv_ffmpeg.c
CSRCS += lv_freetype.c
CSRCS += lv_fs_fatfs.c
CSRCS += lv_fs_posix.c
CSRCS += lv_fs_rawfs.c
CSRCS += lv_fs_stdio.c
CSRCS += lv_fs_win32.c
CSRCS += gifdec.c
CSRCS += lv_gif.c
CSRCS += lodepng.c
CSRCS += lv_png.c
CSRCS += lv_qrcode.c
CSRCS += qrcodegen.c
CSRCS += lv_rlottie.c
CSRCS += lv_sjpg.c
CSRCS += tjpgd.c
CSRCS += lv_extra.c
CSRCS += lv_fragment.c
CSRCS += lv_fragment_manager.c
CSRCS += lv_gridnav.c
CSRCS += lv_ime_pinyin.c
CSRCS += lv_imgfont.c
CSRCS += lv_monkey.c
CSRCS += lv_msg.c
CSRCS += lv_snapshot.c
CSRCS += lv_theme_basic.c
CSRCS += lv_theme_default.c
CSRCS += lv_theme_mono.c
CSRCS += lv_analogclock.c
CSRCS += lv_animimg.c
CSRCS += lv_textprogress.c
CSRCS += lv_calendar.c
CSRCS += lv_calendar_header_arrow.c
CSRCS += lv_calendar_header_dropdown.c
CSRCS += lv_carousel.c
CSRCS += lv_chart.c
CSRCS += lv_colorwheel.c
CSRCS += lv_dclock.c
CSRCS += lv_imgbtn.c
CSRCS += lv_keyboard.c
CSRCS += lv_led.c
CSRCS += lv_list.c
CSRCS += lv_menu.c
CSRCS += lv_meter.c
CSRCS += lv_msgbox.c
CSRCS += lv_radiobtn.c
CSRCS += lv_span.c
CSRCS += lv_spinbox.c
CSRCS += lv_spinner.c
CSRCS += lv_tabview.c
CSRCS += lv_tileview.c
CSRCS += lv_win.c
CSRCS += lv_video.c
CSRCS += lv_zh_keyboard.c

VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/layouts/flex
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/layouts/grid
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/barcode
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/bmp
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/ffmpeg
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/freetype
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/fsdrv
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/gif
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/png
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/qrcode
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/rlottie
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/libs/sjpg
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/fragment
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/gridnav
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/ime
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/imgfont
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/monkey
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/msg
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/others/snapshot
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/themes/basic
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/themes/default
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/themes/mono
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/analogclock
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/animimg
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/calendar
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/carousel
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/chart
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/colorwheel
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/dclock
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/imgbtn
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/keyboard
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/led
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/list
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/menu
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/meter
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/msgbox
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/radiobtn
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/span
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/spinbox
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/spinner
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/tabview
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/textprogress
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/tileview
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/win
VPATH += :$(LVGL_DIR)/$(LVGL_DIR_NAME)/src/extra/widgets/video
